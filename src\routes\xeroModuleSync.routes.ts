import { Router } from 'express';
import {
  getModuleSyncStatusController,
  getSpecificModuleSyncStatusController,
  updateModuleSyncTimeController,
  getAvailableModulesController,
  getSyncSummaryController,
} from '@controllers/xeroModuleSync.controller';
import { authenticate } from '@middlewares/auth.middleware';

// Initialize a new router instance
const router = Router();

/**
 * Xero Module Sync routes:
 *
 * - GET /companies/:companyId/xero/modules/sync-status: Get all module sync status for a company
 * - GET /companies/:companyId/xero/modules/:moduleName/sync-status: Get specific module sync status
 * - PUT /companies/:companyId/xero/modules/:moduleName/sync-time: Update module sync time
 * - GET /companies/:companyId/xero/sync-summary: Get sync summary for a company
 * - GET /xero/modules: Get available Xero modules
 */

// Get all module sync status for a company
router.get('/companies/:companyId/xero/modules/sync-status', authenticate, getModuleSyncStatusController);

// Get sync summary for a company (simplified overview)
router.get('/companies/:companyId/xero/sync-summary', authenticate, getSyncSummaryController);

// Get specific module sync status
router.get('/companies/:companyId/xero/modules/:moduleName/sync-status', authenticate, getSpecificModuleSyncStatusController);

// Update module sync time (for testing/manual updates)
router.put('/companies/:companyId/xero/modules/:moduleName/sync-time', authenticate, updateModuleSyncTimeController);

// Get available Xero modules (no company ID needed)
router.get('/xero/modules', authenticate, getAvailableModulesController);

export default router;
