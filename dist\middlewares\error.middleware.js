"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncErrorHandler = exports.notFoundHandler = exports.errorHandler = exports.InternalServerError = exports.ValidationError = exports.ConflictError = exports.NotFoundError = exports.ForbiddenError = exports.XeroUnauthorizedError = exports.UnauthorizedError = exports.BadRequestError = exports.CustomError = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const response_1 = require("../utils/response");
class CustomError extends Error {
    constructor(message, statusCode = 500, isOperational = true, code, details) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        if (code) {
            this.code = code;
        }
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
class BadRequestError extends CustomError {
    constructor(message = 'Bad Request', code, details) {
        super(message, 400, true, code, details);
    }
}
exports.BadRequestError = BadRequestError;
class UnauthorizedError extends CustomError {
    constructor(message = 'Unauthorized', code, details) {
        super(message, 401, true, code, details);
    }
}
exports.UnauthorizedError = UnauthorizedError;
class XeroUnauthorizedError extends CustomError {
    constructor(message = 'Unauthorized', code, details) {
        super(message, 500, true, code, details);
    }
}
exports.XeroUnauthorizedError = XeroUnauthorizedError;
class ForbiddenError extends CustomError {
    constructor(message = 'Forbidden', code, details) {
        super(message, 403, true, code, details);
    }
}
exports.ForbiddenError = ForbiddenError;
class NotFoundError extends CustomError {
    constructor(message = 'Not Found', code, details) {
        super(message, 404, true, code, details);
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends CustomError {
    constructor(message = 'Conflict', code, details) {
        super(message, 409, true, code, details);
    }
}
exports.ConflictError = ConflictError;
class ValidationError extends CustomError {
    constructor(message = 'Validation Error', details) {
        super(message, 422, true, 'VALIDATION_ERROR', details);
    }
}
exports.ValidationError = ValidationError;
class InternalServerError extends CustomError {
    constructor(message = 'Internal Server Error', code, details) {
        super(message, 500, false, code, details);
    }
}
exports.InternalServerError = InternalServerError;
const errorHandler = (err, req, res, _next) => {
    const statusCode = err.statusCode || 500;
    const isOperational = err.isOperational !== undefined ? err.isOperational : false;
    const isDevelopment = process.env['NODE_ENV'] === 'development';
    const correlationId = req.headers['x-correlation-id'] ||
        `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const errorContext = {
        message: err.message,
        stack: err.stack,
        statusCode,
        isOperational,
        code: err.code,
        details: err.details,
        correlationId,
        request: {
            method: req.method,
            url: req.originalUrl,
            ip: req.ip || req.socket.remoteAddress,
            userAgent: req.get('User-Agent'),
            headers: isDevelopment ? req.headers : undefined,
            body: isDevelopment && req.method !== 'GET' ? req.body : undefined,
            params: req.params,
            query: req.query,
            userId: req.user?.userId || req.user?.id,
        },
        timestamp: new Date().toISOString(),
        environment: process.env['NODE_ENV'] || 'development',
    };
    if (statusCode >= 500) {
        logger_1.default.error('Server Error', errorContext);
    }
    else if (statusCode >= 400) {
    }
    else {
    }
    let responseMessage = 'Internal server error';
    let responseDetails = undefined;
    if (isOperational || isDevelopment) {
        responseMessage = err.message;
        responseDetails = err.details;
    }
    const errorResponseData = {
        correlationId,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
        ...(isDevelopment && {
            stack: err.stack,
            code: err.code,
            isOperational,
        }),
        ...(responseDetails && { details: responseDetails }),
    };
    res.status(statusCode).json((0, response_1.errorResponse)(responseMessage, errorResponseData));
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, _res, next) => {
    const error = new NotFoundError(`Route ${req.method} ${req.originalUrl} not found`, 'ROUTE_NOT_FOUND');
    next(error);
};
exports.notFoundHandler = notFoundHandler;
const asyncErrorHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncErrorHandler = asyncErrorHandler;
//# sourceMappingURL=error.middleware.js.map