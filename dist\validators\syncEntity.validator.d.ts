import { z } from "zod";
export declare const allowedEntities: readonly ["Bank Transactions", "Bank Transfers", "Credit Notes", "Employees", "Expense Claims", "Invoices", "Journals", "Manual Journals", "Payments", "Tracking Categories", "Tax Rates", "Attachments", "Profit and Loss with Tracking", "Profit and Loss without Tracking", "(P&L, Balance Sheet, Trial Balance)", "Balance Sheet without Tracking", "Trial Balance"];
export type AllowedEntity = typeof allowedEntities[number];
export declare const syncEntitySchema: z.ZodEnum<["Bank Transactions", "Bank Transfers", "Credit Notes", "Employees", "Expense Claims", "Invoices", "Journals", "Manual Journals", "Payments", "Tracking Categories", "Tax Rates", "Attachments", "Profit and Loss with Tracking", "Profit and Loss without Tracking", "(P&L, Balance Sheet, Trial Balance)", "Balance Sheet without Tracking", "Trial Balance"]>;
//# sourceMappingURL=syncEntity.validator.d.ts.map