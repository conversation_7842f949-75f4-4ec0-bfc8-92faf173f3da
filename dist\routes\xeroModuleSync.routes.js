"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const xeroModuleSync_controller_1 = require("@controllers/xeroModuleSync.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/companies/:companyId/xero/modules/sync-status', auth_middleware_1.authenticate, xeroModuleSync_controller_1.getModuleSyncStatusController);
router.get('/companies/:companyId/xero/sync-summary', auth_middleware_1.authenticate, xeroModuleSync_controller_1.getSyncSummaryController);
router.get('/companies/:companyId/xero/modules/:moduleName/sync-status', auth_middleware_1.authenticate, xeroModuleSync_controller_1.getSpecificModuleSyncStatusController);
router.put('/companies/:companyId/xero/modules/:moduleName/sync-time', auth_middleware_1.authenticate, xeroModuleSync_controller_1.updateModuleSyncTimeController);
router.get('/xero/modules', auth_middleware_1.authenticate, xeroModuleSync_controller_1.getAvailableModulesController);
exports.default = router;
//# sourceMappingURL=xeroModuleSync.routes.js.map