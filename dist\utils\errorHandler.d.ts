import { ZodError } from 'zod';
import { ValidationError, UnauthorizedError, AppError } from '@middlewares/error.middleware';
export declare const handleZodError: (error: ZodError) => ValidationError;
export declare const handlePrismaError: (error: any) => AppError;
export declare const handleJWTError: (error: any) => UnauthorizedError;
export declare const handleError: (error: any) => AppError;
export declare const safeAsync: <T extends any[], R>(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>;
//# sourceMappingURL=errorHandler.d.ts.map