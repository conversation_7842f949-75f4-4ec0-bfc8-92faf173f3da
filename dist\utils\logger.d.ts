import winston from 'winston';
interface CustomLogger extends winston.Logger {
    info(message: string, ...meta: any[]): this;
    error(message: string, ...meta: any[]): this;
    warn(message: string, ...meta: any[]): this;
    debug(message: string, ...meta: any[]): this;
    verbose(message: string, ...meta: any[]): this;
    silly(message: string, ...meta: any[]): this;
    stream: {
        write(message: string): void;
    };
    logRequest(req: {
        method: string;
        originalUrl: string;
        user?: {
            id?: string;
        };
        ip?: string;
        get(header: string): string | undefined;
    }, res: {
        statusCode: number;
    }, responseTime: number): void;
    logError(error: Error, context?: Record<string, unknown>): void;
    logSecurity(event: string, details: Record<string, unknown>): void;
}
declare const _default: CustomLogger;
export default _default;
//# sourceMappingURL=logger.d.ts.map