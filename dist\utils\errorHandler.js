"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.safeAsync = exports.handleError = exports.handleJWTError = exports.handlePrismaError = exports.handleZodError = void 0;
const zod_1 = require("zod");
const jsonwebtoken_1 = require("jsonwebtoken");
const error_middleware_1 = require("@middlewares/error.middleware");
const handleZodError = (error) => {
    const formattedErrors = error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
    }));
    return new error_middleware_1.ValidationError('Validation failed', {
        errors: formattedErrors,
        totalErrors: error.errors.length,
    });
};
exports.handleZodError = handleZodError;
const handlePrismaError = (error) => {
    if (error.name === 'PrismaClientKnownRequestError' && 'code' in error) {
        switch (error.code) {
            case 'P2002': {
                const fields = error.meta?.target || ['field'];
                return new error_middleware_1.ConflictError(`${fields.join(', ')} already exists`, 'UNIQUE_CONSTRAINT_VIOLATION', { fields, constraint: error.meta?.constraint });
            }
            case 'P2025':
                return new error_middleware_1.BadRequestError('Record not found', 'RECORD_NOT_FOUND', {
                    operation: error.meta?.cause,
                });
            case 'P2003':
                return new error_middleware_1.BadRequestError('Invalid reference to related record', 'FOREIGN_KEY_CONSTRAINT', { field: error.meta?.field_name });
            case 'P2014':
                return new error_middleware_1.BadRequestError('Required relation is missing', 'REQUIRED_RELATION_VIOLATION', {
                    relation: error.meta?.relation_name,
                });
            default:
                return new error_middleware_1.InternalServerError('Database operation failed', 'DATABASE_ERROR', {
                    code: error.code,
                    meta: error.meta,
                });
        }
    }
    switch (error.name) {
        case 'PrismaClientUnknownRequestError':
            return new error_middleware_1.InternalServerError('Unknown database error occurred', 'DATABASE_UNKNOWN_ERROR');
        case 'PrismaClientRustPanicError':
            return new error_middleware_1.InternalServerError('Database engine error', 'DATABASE_ENGINE_ERROR');
        case 'PrismaClientInitializationError':
            return new error_middleware_1.InternalServerError('Database connection failed', 'DATABASE_CONNECTION_ERROR');
        case 'PrismaClientValidationError':
            return new error_middleware_1.BadRequestError('Invalid database query', 'DATABASE_VALIDATION_ERROR');
        default:
            return new error_middleware_1.InternalServerError('Unhandled database error', 'DATABASE_ERROR');
    }
};
exports.handlePrismaError = handlePrismaError;
const handleJWTError = (error) => {
    if (error instanceof jsonwebtoken_1.TokenExpiredError) {
        return new error_middleware_1.UnauthorizedError('Token has expired', 'TOKEN_EXPIRED', {
            expiredAt: error.expiredAt,
        });
    }
    if (error instanceof jsonwebtoken_1.JsonWebTokenError) {
        return new error_middleware_1.UnauthorizedError('Invalid token', 'INVALID_TOKEN', { message: error.message });
    }
    return new error_middleware_1.UnauthorizedError('Authentication failed', 'AUTH_ERROR');
};
exports.handleJWTError = handleJWTError;
const handleError = (error) => {
    if (error.statusCode && error.isOperational !== undefined) {
        return error;
    }
    if (error instanceof zod_1.ZodError) {
        return (0, exports.handleZodError)(error);
    }
    if (error.name?.includes('Prisma') || error.name === 'PrismaClientKnownRequestError') {
        return (0, exports.handlePrismaError)(error);
    }
    if (error instanceof jsonwebtoken_1.JsonWebTokenError || error instanceof jsonwebtoken_1.TokenExpiredError) {
        return (0, exports.handleJWTError)(error);
    }
    if (error.code === 'ECONNREFUSED') {
        return new error_middleware_1.InternalServerError('Service unavailable', 'SERVICE_UNAVAILABLE');
    }
    if (error.code === 'ENOTFOUND') {
        return new error_middleware_1.InternalServerError('External service not found', 'EXTERNAL_SERVICE_ERROR');
    }
    return new error_middleware_1.InternalServerError(error.message || 'An unexpected error occurred', 'UNKNOWN_ERROR', process.env['NODE_ENV'] === 'development' ? { originalError: error } : undefined);
};
exports.handleError = handleError;
const safeAsync = (fn) => {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            throw (0, exports.handleError)(error);
        }
    };
};
exports.safeAsync = safeAsync;
//# sourceMappingURL=errorHandler.js.map