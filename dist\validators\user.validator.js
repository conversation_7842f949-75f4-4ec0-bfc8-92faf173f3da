"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.changePasswordSchema = exports.updateProfileSchema = exports.loginSchema = exports.registerSchema = void 0;
const zod_1 = require("zod");
const passwordSchema = zod_1.z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password must not exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character (@$!%*?&)');
const emailSchema = zod_1.z
    .string()
    .min(1, 'Email is required')
    .max(254, 'Email must not exceed 254 characters')
    .email('Please enter a valid email address')
    .regex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Email format is invalid')
    .transform((email) => email.toLowerCase().trim());
const nameSchema = zod_1.z
    .string()
    .min(1, 'Name cannot be empty')
    .max(100, 'Name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
    .transform((name) => name.trim())
    .optional();
exports.registerSchema = zod_1.z.object({
    name: nameSchema,
    email: emailSchema,
    password: passwordSchema,
});
exports.loginSchema = zod_1.z.object({
    email: emailSchema,
    password: zod_1.z
        .string()
        .min(1, 'Password is required')
        .max(128, 'Password must not exceed 128 characters'),
});
exports.updateProfileSchema = zod_1.z.object({
    name: nameSchema,
    email: emailSchema.optional(),
});
exports.changePasswordSchema = zod_1.z.object({
    currentPassword: zod_1.z
        .string()
        .min(1, 'Current password is required')
        .max(128, 'Password must not exceed 128 characters'),
    newPassword: passwordSchema,
});
//# sourceMappingURL=user.validator.js.map