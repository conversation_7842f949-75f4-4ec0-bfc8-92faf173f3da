import { Request, Response, NextFunction } from 'express';
declare module 'express-serve-static-core' {
    interface Request {
        user?: any;
    }
}
export declare const getCompanies: (req: Request, res: Response, next: NextFunction) => void;
export declare const getCompanyStats: (req: Request, res: Response, next: NextFunction) => void;
export declare const getCompanyByIdController: (req: Request, res: Response, next: NextFunction) => void;
export declare const disconnectCompanyXeroController: (req: Request, res: Response, next: NextFunction) => void;
export declare const reconnectCompanyXeroController: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=company.controller.d.ts.map