"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkSyncSchema = exports.syncWebhookSchema = exports.companyIdParamSchema = exports.triggerAllSyncSchema = exports.syncHistoryQuerySchema = exports.syncTriggerSchema = exports.syncStatusQuerySchema = void 0;
const zod_1 = require("zod");
const sync_service_1 = require("@services/sync.service");
exports.syncStatusQuerySchema = zod_1.z.object({
    companyId: zod_1.z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
});
exports.syncTriggerSchema = zod_1.z.object({
    companyId: zod_1.z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
    entities: zod_1.z
        .array(zod_1.z.enum(sync_service_1.SYNC_ENTITIES))
        .min(1, 'At least one entity must be specified')
        .max(sync_service_1.SYNC_ENTITIES.length, `Maximum ${sync_service_1.SYNC_ENTITIES.length} entities allowed`)
        .refine((entities) => {
        const uniqueEntities = new Set(entities);
        return uniqueEntities.size === entities.length;
    }, {
        message: 'Duplicate entities are not allowed',
    }),
    priority: zod_1.z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
    fullSync: zod_1.z.boolean().optional().default(false),
});
exports.syncHistoryQuerySchema = zod_1.z
    .object({
    companyId: zod_1.z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
    limit: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? parseInt(val, 10) : 50))
        .refine((val) => val >= 1 && val <= 200, {
        message: 'Limit must be between 1 and 200',
    }),
    offset: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? parseInt(val, 10) : 0))
        .refine((val) => val >= 0, {
        message: 'Offset must be non-negative',
    }),
    entity: zod_1.z.enum(sync_service_1.SYNC_ENTITIES).optional(),
    status: zod_1.z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
    dateFrom: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? new Date(val) : undefined))
        .refine((val) => {
        if (val === undefined)
            return true;
        return !isNaN(val.getTime());
    }, {
        message: 'dateFrom must be a valid ISO date string',
    }),
    dateTo: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? new Date(val) : undefined))
        .refine((val) => {
        if (val === undefined)
            return true;
        return !isNaN(val.getTime());
    }, {
        message: 'dateTo must be a valid ISO date string',
    }),
})
    .refine((data) => {
    if (data.dateFrom && data.dateTo) {
        return data.dateFrom <= data.dateTo;
    }
    return true;
}, {
    message: 'dateFrom must be before or equal to dateTo',
    path: ['dateFrom'],
});
exports.triggerAllSyncSchema = zod_1.z.object({
    companyId: zod_1.z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
    priority: zod_1.z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
    fullSync: zod_1.z.boolean().optional().default(false),
});
exports.companyIdParamSchema = zod_1.z.object({
    id: zod_1.z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
});
exports.syncWebhookSchema = zod_1.z.object({
    syncId: zod_1.z.string().uuid('Sync ID must be a valid UUID'),
    companyId: zod_1.z.string().uuid('Company ID must be a valid UUID'),
    entityType: zod_1.z.enum(sync_service_1.SYNC_ENTITIES),
    status: zod_1.z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED']),
    recordsProcessed: zod_1.z.number().int().min(0).optional(),
    recordsSucceeded: zod_1.z.number().int().min(0).optional(),
    recordsFailed: zod_1.z.number().int().min(0).optional(),
    errorMessage: zod_1.z.string().optional(),
    duration: zod_1.z.number().int().min(0).optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
exports.bulkSyncSchema = zod_1.z.object({
    companies: zod_1.z
        .array(zod_1.z.object({
        companyId: zod_1.z.string().uuid('Company ID must be a valid UUID'),
        entities: zod_1.z
            .array(zod_1.z.enum(sync_service_1.SYNC_ENTITIES))
            .optional()
            .default([...sync_service_1.SYNC_ENTITIES]),
    }))
        .min(1, 'At least one company must be specified')
        .max(10, 'Maximum 10 companies allowed per bulk operation'),
    priority: zod_1.z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
    fullSync: zod_1.z.boolean().optional().default(false),
});
//# sourceMappingURL=sync.validator.js.map