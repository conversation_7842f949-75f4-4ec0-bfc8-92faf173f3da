import { Request, Response, NextFunction } from 'express';
declare module 'express-serve-static-core' {
    interface Request {
        user?: any;
    }
}
export declare const generateAuthUrl: (req: Request, res: Response, next: NextFunction) => void;
export declare const xeroCallback: (req: Request, res: Response, next: NextFunction) => void;
export declare const xeroDisconnect: (req: Request, res: Response, next: NextFunction) => void;
export declare const refreshXeroToken: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=xero.controller.d.ts.map