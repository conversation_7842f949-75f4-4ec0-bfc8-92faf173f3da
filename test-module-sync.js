// Simple test script to verify the module sync functionality
// This bypasses TypeScript compilation issues

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Test data
const XERO_MODULES = [
  'Accounts',
  'Bank Transactions',
  'Bank Transfers',
  'Budgets',
  'Contacts',
  'Credit Notes',
  'Currencies',
  'Employees',
  'Expense Claims',
  'Invoices',
  'Journals',
  'Manual Journals',
  'Payments',
  'Tracking Categories',
  'Tax Rates',
  'Attachments',
  'Reports (P&L, BS, TB)',
];

async function testModuleSyncFunctionality() {
  try {
    console.log('🧪 Testing Xero Module Sync Functionality...\n');

    // Test 1: Check if XeroModuleSync table exists and has correct structure
    console.log('1. Testing database schema...');
    
    try {
      const tableInfo = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'XeroModuleSync' 
        ORDER BY ordinal_position;
      `;
      
      console.log('✅ XeroModuleSync table structure:');
      tableInfo.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
    } catch (error) {
      console.log('❌ Error checking table structure:', error.message);
      return;
    }

    // Test 2: Check if we can create a test company
    console.log('\n2. Testing company creation...');
    
    const testCompany = await prisma.company.create({
      data: {
        Name: 'Test Company for Module Sync',
        UserId: '00000000-0000-0000-0000-000000000001', // Dummy UUID
        XeroTenantId: 'test-tenant-' + Date.now(),
        ConnectionStatus: 'ACTIVE',
        FinancialYearEnd: '31-03', // March 31st
      },
    });
    
    console.log('✅ Test company created:', {
      id: testCompany.Id,
      name: testCompany.Name,
      financialYearEnd: testCompany.FinancialYearEnd,
    });

    // Test 3: Create module sync records
    console.log('\n3. Testing module sync record creation...');
    
    const moduleRecords = XERO_MODULES.map(moduleName => ({
      CompanyId: testCompany.Id,
      ModuleName: moduleName,
      LastSyncTime: null,
    }));

    await prisma.xeroModuleSync.createMany({
      data: moduleRecords,
      skipDuplicates: true,
    });

    console.log('✅ Created module sync records for all', XERO_MODULES.length, 'modules');

    // Test 4: Retrieve module sync status
    console.log('\n4. Testing module sync status retrieval...');
    
    const syncRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: testCompany.Id },
      orderBy: { ModuleName: 'asc' },
    });

    console.log('✅ Retrieved', syncRecords.length, 'module sync records');
    console.log('   Sample records:');
    syncRecords.slice(0, 3).forEach(record => {
      console.log(`   - ${record.ModuleName}: ${record.LastSyncTime || 'Not synced yet'}`);
    });

    // Test 5: Update a module sync time
    console.log('\n5. Testing module sync time update...');
    
    const updatedRecord = await prisma.xeroModuleSync.update({
      where: {
        CompanyId_ModuleName: {
          CompanyId: testCompany.Id,
          ModuleName: 'Accounts',
        },
      },
      data: {
        LastSyncTime: new Date(),
      },
    });

    console.log('✅ Updated sync time for Accounts module:', updatedRecord.LastSyncTime);

    // Test 6: Check sync progress
    console.log('\n6. Testing sync progress calculation...');
    
    const allRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: testCompany.Id },
    });

    const totalModules = allRecords.length;
    const syncedModules = allRecords.filter(record => record.LastSyncTime !== null).length;
    const syncProgress = Math.round((syncedModules / totalModules) * 100);

    console.log('✅ Sync progress:', {
      totalModules,
      syncedModules,
      pendingModules: totalModules - syncedModules,
      syncProgress: syncProgress + '%',
    });

    // Test 7: Test unique constraint
    console.log('\n7. Testing unique constraint...');
    
    try {
      await prisma.xeroModuleSync.create({
        data: {
          CompanyId: testCompany.Id,
          ModuleName: 'Accounts', // This should fail due to unique constraint
          LastSyncTime: null,
        },
      });
      console.log('❌ Unique constraint test failed - duplicate record was created');
    } catch (error) {
      if (error.code === 'P2002') {
        console.log('✅ Unique constraint working correctly - duplicate prevented');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Cleanup
    console.log('\n8. Cleaning up test data...');
    
    await prisma.xeroModuleSync.deleteMany({
      where: { CompanyId: testCompany.Id },
    });

    await prisma.company.delete({
      where: { Id: testCompany.Id },
    });

    console.log('✅ Test data cleaned up');

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database schema is correct');
    console.log('   ✅ Module sync records can be created');
    console.log('   ✅ Module sync status can be retrieved');
    console.log('   ✅ Module sync times can be updated');
    console.log('   ✅ Sync progress can be calculated');
    console.log('   ✅ Unique constraints are working');
    console.log('   ✅ Financial year is stored correctly');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testModuleSyncFunctionality();
