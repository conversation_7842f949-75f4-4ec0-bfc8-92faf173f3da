import { prisma, config } from '@config/config';
import axios from 'axios';
import qs from 'qs'; // to build query string
import logger from '@utils/logger';

import {
  UnauthorizedError,
  BadRequestError,
  XeroUnauthorizedError,
} from '@middlewares/error.middleware';

/**
 * Interface for company listing filters
 */
export interface CompanyListFilters {
  name?: string;
  connectionStatus?: 'ACTIVE' | 'EXPIRED' | 'DISCONNECTED' | 'PENDING';
  hasXeroConnection?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Builds the Xero OAuth2 authorization URL
 * using environment variables defined in .env.
 */
export const getXeroAuthUrl = (): string => {
  // Scope defines which Xero API data your app can access
  const scope = [
    'openid',
    'profile',
    'email',
    'offline_access',
    'accounting.transactions',
    'accounting.settings',
    'accounting.contacts',
    'accounting.reports.read',
  ].join(' ');

  const authUrl = `${config.XERO.XERO_AUTH_URL}?${qs.stringify({
    response_type: 'code',
    client_id: config.XERO.XERO_CLIENT_ID,
    redirect_uri: config.XERO.XERO_REDIRECT_URI,
    scope,
    state: 'companyId-placeholder', // REPLACE at runtime with companyId if needed
  })}`;

  return authUrl;
};

/**
 * Handles Xero OAuth2 callback:
 * exchanges authorization code for access/refresh tokens,
 * and updates the Company model with those tokens.
 */
export const handleXeroCallback = async (code: string, userId: string) => {
  try {
    // STEP 1: Exchange the code for tokens
    const tokenResponse = await axios.post(
      config.XERO.XERO_TOKEN_URL as string,
      qs.stringify({
        grant_type: 'authorization_code',
        code,
        redirect_uri: config.XERO.XERO_REDIRECT_URI,
        client_id: config.XERO.XERO_CLIENT_ID,
        client_secret: config.XERO.XERO_CLIENT_SECRET,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const { access_token, refresh_token, expires_in } = tokenResponse.data;

    logger.debug('Xero token exchange completed successfully', {
      accessTokenLength: access_token.length,
      refreshTokenLength: refresh_token.length,
      expiresIn: expires_in,
    });

    // STEP 2: Call Xero connections API to get tenant (organization) details
    const tenantResponse = await axios.get('https://api.xero.com/connections', {
      headers: {
        Authorization: `Bearer ${access_token}`,
        Accept: 'application/json',
      },
    });

    const tenants = tenantResponse.data as Array<{
      tenantId: string;
      tenantName: string;
      tenantType: string;
      createdDateUtc: string;
      updatedDateUtc: string;
    }>;

    if (!tenants || tenants.length === 0) {
      throw new XeroUnauthorizedError(
        'No active Xero tenant found for this account.',
        'XERO_NO_TENANT'
      );
    }

    // We'll take the first tenant as the active company
    const tenant: any = tenants[0];

    // STEP 3: Check if a company already exists for this user + tenant
    const existingCompany = await prisma.company.findFirst({
      where: {
        XeroTenantId: tenant.tenantId,
      },
    });
    console.log("existingCompany", existingCompany);
    let updatedCompany;

    if (existingCompany) {
      // Company exists: update its tokens and tenant info
      updatedCompany = await prisma.company.update({
        where: { Id: existingCompany.Id },
        data: {
          XeroAccessToken: access_token,
          XeroRefreshToken: refresh_token,
          XeroTenantId: tenant.tenantId,
          Name: tenant.tenantName,
          XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
          XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
          ConnectionStatus: 'ACTIVE',
        },
      });
    } else {
      // No company exists: create a new one
      updatedCompany = await prisma.company.create({
        data: {
          Name: tenant.tenantName,
          XeroTenantId: tenant.tenantId,
          XeroAccessToken: access_token,
          XeroRefreshToken: refresh_token,
          XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
          UserId: userId,
          XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
          ConnectionStatus: 'ACTIVE',
        },
      });
    }

    return updatedCompany;
  } catch (error: any) {
    logger.error('Error in handleXeroCallback', {
      error: error.message,
      stack: error.stack,
      userId,
      code: `${code?.substring(0, 10)}...`, // Log partial code for debugging
    });
    throw new UnauthorizedError(
      `Failed to complete Xero auth flow: ${error.message}`,
      'XERO_CALLBACK_FAILED'
    );
  }
};

/**
 * Disconnects Xero by wiping tokens from the Company.
 */
export const disconnectXero = async (companyId: string) => {
  // Look up the company
  const company = await prisma.company.findUnique({
    where: { Id: companyId },
  });

  if (!company || !company.XeroAccessToken) {
    throw new UnauthorizedError(
      'Company has no active Xero connection to disconnect.',
      'NO_XERO_CONNECTION'
    );
  }

  try {
    // Step 1: revoke the access token with Xero
    await axios.post(
      'https://identity.xero.com/connect/revocation',
      qs.stringify({
        token: company.XeroRefreshToken,
        client_id: config.XERO.XERO_CLIENT_ID,
        client_secret: config.XERO.XERO_CLIENT_SECRET,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    // Step 2: clear local company Xero fields
    await prisma.company.update({
      where: { Id: companyId },
      data: {
        XeroAccessToken: null,
        XeroRefreshToken: null,
        XeroTokenExpiry: null,
        XeroRefreshTokenExpiry: null,
        ConnectionStatus: 'DISCONNECTED',
      },
    });
  } catch (error: any) {
    logger.error('Xero disconnect error', {
      error: error.message,
      stack: error.stack,
      companyId,
    });
    throw new UnauthorizedError('Failed to disconnect Xero account.', 'XERO_DISCONNECT_FAILED');
  }
};

/**
 * Refreshes Xero tokens given a valid refresh token.
 */
export const refreshXeroTokens = async (companyId: string) => {
  // Load current tokens
  const company = await prisma.company.findUnique({
    where: { Id: companyId },
  });

  if (!company?.XeroRefreshToken) {
    throw new BadRequestError('No Xero refresh token found for this company.', 'NO_REFRESH_TOKEN');
  }

  try {
    // exchange refresh token for new tokens
    const response = await axios.post(
      config.XERO.XERO_TOKEN_URL as string,
      qs.stringify({
        grant_type: 'refresh_token',
        refresh_token: company.XeroRefreshToken,
        client_id: config.XERO.XERO_CLIENT_ID,
        client_secret: config.XERO.XERO_CLIENT_SECRET,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const { access_token, refresh_token, expires_in } = response.data;

    const updatedCompany = await prisma.company.update({
      where: { Id: companyId },
      data: {
        XeroAccessToken: access_token,
        XeroRefreshToken: refresh_token,
        XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
      },
    });

    return updatedCompany;
  } catch (error: any) {
    throw new UnauthorizedError(
      `Failed to refresh Xero token: ${error.message}`,
      'XERO_REFRESH_FAILED'
    );
  }
};

/**
 * Get companies with filtering, pagination, and sorting
 * @param userId - The user ID to filter companies for
 * @param filters - Filter options for company listing
 * @returns Promise with companies list and metadata
 */
export const getCompaniesWithFilters = async (userId: string, filters: CompanyListFilters = {}) => {
  try {
    const {
      name,
      connectionStatus,
      hasXeroConnection,
      createdAfter,
      createdBefore,
      limit = 10,
      offset = 0,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    // Build where clause for filtering
    const whereClause: any = {
      UserId: userId,
    };

    // Name filter (case-insensitive partial match)
    if (name) {
      whereClause.Name = {
        contains: name,
        mode: 'insensitive',
      };
    }

    // Connection status filter
    if (connectionStatus) {
      whereClause.ConnectionStatus = connectionStatus;
    }

    // Xero connection filter
    if (hasXeroConnection !== undefined) {
      if (hasXeroConnection) {
        whereClause.XeroTenantId = {
          not: null,
        };
        whereClause.XeroAccessToken = {
          not: null,
        };
      } else {
        whereClause.OR = [{ XeroTenantId: null }, { XeroAccessToken: null }];
      }
    }

    // Date range filters
    if (createdAfter || createdBefore) {
      whereClause.CreatedAt = {};
      if (createdAfter) {
        whereClause.CreatedAt.gte = createdAfter;
      }
      if (createdBefore) {
        whereClause.CreatedAt.lte = createdBefore;
      }
    }

    // Build order by clause
    const orderBy: any = {};
    if (sortBy === 'name') {
      orderBy.Name = sortOrder;
    } else if (sortBy === 'updatedAt') {
      orderBy.UpdatedAt = sortOrder;
    } else {
      orderBy.CreatedAt = sortOrder;
    }

    // Execute queries
    const [companies, totalCount] = await Promise.all([
      prisma.company.findMany({
        where: whereClause,
        orderBy,
        take: Math.min(limit, 100), // Cap at 100 for performance
        skip: offset,
        select: {
          Id: true,
          Name: true,
          XeroTenantId: true,
          ConnectionStatus: true,
          FinancialYearEnd: true,
          CreatedAt: true,
          UpdatedAt: true,
          XeroTokenExpiry: true,
          // Don't expose sensitive token data
        },
      }),
      prisma.company.count({
        where: whereClause,
      }),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = Math.floor(offset / limit) + 1;
    const hasNextPage = offset + limit < totalCount;
    const hasPreviousPage = offset > 0;

    logger.info('Companies retrieved successfully', {
      userId,
      totalCount,
      returnedCount: companies.length,
      filters,
    });

    return {
      companies,
      pagination: {
        totalCount,
        totalPages,
        currentPage,
        limit,
        offset,
        hasNextPage,
        hasPreviousPage,
      },
      filters,
    };
  } catch (error: any) {
    logger.error('Failed to retrieve companies', {
      userId,
      filters,
      error: error.message,
    });
    throw new BadRequestError(
      `Failed to retrieve companies: ${error.message}`,
      'COMPANY_RETRIEVAL_FAILED'
    );
  }
};

/**
 * Get a single company by ID for the authenticated user
 * @param userId - The user ID to ensure ownership
 * @param companyId - The company ID to retrieve
 * @returns Promise with company data or null if not found
 */
export const getCompanyById = async (userId: string, companyId: string) => {
  try {
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId, // Ensure user owns this company
      },
      select: {
        Id: true,
        Name: true,
        XeroTenantId: true,
        ConnectionStatus: true,
        FinancialYearEnd: true,
        CreatedAt: true,
        UpdatedAt: true,
        XeroTokenExpiry: true,
        XeroRefreshTokenExpiry: true,
        // Include user info for context
        User: {
          select: {
            Id: true,
            Email: true,
            Name: true,
          },
        },
        // Don't expose sensitive token data
      },
    });

    if (!company) {
      logger.warn('Company not found or access denied', {
        userId,
        companyId,
      });
      return null;
    }

    logger.info('Company retrieved successfully', {
      userId,
      companyId: company.Id,
      companyName: company.Name,
    });

    return company;
  } catch (error: any) {
    logger.error('Failed to retrieve company', {
      userId,
      companyId,
      error: error.message,
    });
    throw new BadRequestError(
      `Failed to retrieve company: ${error.message}`,
      'COMPANY_RETRIEVAL_FAILED'
    );
  }
};

/**
 * Disconnect a company's Xero connection
 * Clears all Xero-related tokens and sets status to DISCONNECTED
 * @param userId - The user ID to ensure ownership
 * @param companyId - The company ID to disconnect
 * @returns Promise with updated company data
 */
export const disconnectCompanyXero = async (userId: string, companyId: string) => {
  try {
    // First verify the company exists and belongs to the user
    const existingCompany = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId,
      },
    });

    if (!existingCompany) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Check if company has an active Xero connection
    if (!existingCompany.XeroTenantId && !existingCompany.XeroAccessToken) {
      throw new BadRequestError(
        'Company does not have an active Xero connection',
        'NO_XERO_CONNECTION'
      );
    }

    // Update company to disconnect Xero
    const updatedCompany = await prisma.company.update({
      where: {
        Id: companyId,
      },
      data: {
        XeroAccessToken: null,
        XeroRefreshToken: null,
        XeroTokenExpiry: null,
        XeroRefreshTokenExpiry: null,
        ConnectionStatus: 'DISCONNECTED',
        UpdatedAt: new Date(),
      },
      select: {
        Id: true,
        Name: true,
        XeroTenantId: true,
        ConnectionStatus: true,
        FinancialYearEnd: true,
        CreatedAt: true,
        UpdatedAt: true,
        XeroTokenExpiry: true,
        // Don't expose sensitive data
      },
    });

    logger.info('Company Xero connection disconnected successfully', {
      userId,
      companyId: updatedCompany.Id,
      companyName: updatedCompany.Name,
      previousStatus: existingCompany.ConnectionStatus,
      newStatus: updatedCompany.ConnectionStatus,
    });

    return updatedCompany;
  } catch (error: any) {
    // Re-throw known errors
    if (error instanceof BadRequestError) {
      throw error;
    }

    logger.error('Failed to disconnect company Xero connection', {
      userId,
      companyId,
      error: error.message,
    });
    throw new BadRequestError(
      `Failed to disconnect Xero connection: ${error.message}`,
      'XERO_DISCONNECT_FAILED'
    );
  }
};

/**
 * Reconnect company's Xero connection by initiating OAuth flow
 * @param userId - The user ID to ensure ownership
 * @param companyId - The company ID to reconnect
 * @returns Promise with authorization URL for reconnection
 */
export const reconnectCompanyXero = async (userId: string, companyId: string) => {
  try {
    // Verify company exists and belongs to user
    const existingCompany = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId,
      },
      select: {
        Id: true,
        Name: true,
        ConnectionStatus: true,
        XeroTenantId: true,
        XeroAccessToken: true,
        XeroRefreshToken: true,
      },
    });

    if (!existingCompany) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Check if company is already connected
    if (existingCompany.ConnectionStatus === 'ACTIVE' && existingCompany.XeroAccessToken) {
      throw new BadRequestError(
        'Company already has an active Xero connection. Disconnect first if you want to reconnect.',
        'ALREADY_CONNECTED'
      );
    }

    // Update company status to PENDING for reconnection
    await prisma.company.update({
      where: {
        Id: companyId,
      },
      data: {
        ConnectionStatus: 'PENDING',
        UpdatedAt: new Date(),
      },
    });

    // Generate authorization URL with company ID in state
    const authUrl = getXeroAuthUrl().replace('companyId-placeholder', companyId);

    logger.info('Company Xero reconnection initiated', {
      userId,
      companyId: existingCompany.Id,
      companyName: existingCompany.Name,
      previousStatus: existingCompany.ConnectionStatus,
      newStatus: 'PENDING',
    });

    return {
      authorizationUrl: authUrl,
      companyId,
      companyName: existingCompany.Name,
    };
  } catch (error: any) {
    // Re-throw known errors
    if (error instanceof BadRequestError) {
      throw error;
    }

    logger.error('Failed to initiate company Xero reconnection', {
      userId,
      companyId,
      error: error.message,
    });

    throw new BadRequestError(
      `Failed to initiate Xero reconnection: ${error.message}`,
      'XERO_RECONNECT_FAILED'
    );
  }
};
