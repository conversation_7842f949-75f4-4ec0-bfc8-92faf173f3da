"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.areAllModulesSynced = exports.getModuleSyncStatusByName = exports.updateModuleSyncTime = exports.getModuleSyncStatus = exports.createInitialModuleSyncRecords = exports.XERO_MODULES = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
exports.XERO_MODULES = [
    'Accounts',
    'Bank Transactions',
    'Bank Transfers',
    'Budgets',
    'Contacts',
    'Credit Notes',
    'Currencies',
    'Employees',
    'Expense Claims',
    'Invoices',
    'Journals',
    'Manual Journals',
    'Payments',
    'Tracking Categories',
    'Tax Rates',
    'Attachments',
    'Reports (P&L, BS, TB)',
];
const createInitialModuleSyncRecords = async (companyId) => {
    try {
        logger_1.default.info('Creating initial module sync records', { companyId });
        const existingRecords = await config_1.prisma.xeroModuleSync.findMany({
            where: { CompanyId: companyId },
        });
        if (existingRecords.length > 0) {
            logger_1.default.info('Module sync records already exist for company', {
                companyId,
                existingCount: existingRecords.length
            });
            return existingRecords.map(record => ({
                id: record.Id,
                companyId: record.CompanyId,
                moduleName: record.ModuleName,
                lastSyncTime: record.LastSyncTime,
                createdAt: record.CreatedAt,
                updatedAt: record.UpdatedAt,
            }));
        }
        const moduleRecords = exports.XERO_MODULES.map(moduleName => ({
            CompanyId: companyId,
            ModuleName: moduleName,
            LastSyncTime: null,
        }));
        await config_1.prisma.xeroModuleSync.createMany({
            data: moduleRecords,
            skipDuplicates: true,
        });
        const createdRecords = await config_1.prisma.xeroModuleSync.findMany({
            where: { CompanyId: companyId },
            orderBy: { ModuleName: 'asc' },
        });
        logger_1.default.info('Successfully created module sync records', {
            companyId,
            recordCount: createdRecords.length
        });
        return createdRecords.map(record => ({
            id: record.Id,
            companyId: record.CompanyId,
            moduleName: record.ModuleName,
            lastSyncTime: record.LastSyncTime,
            createdAt: record.CreatedAt,
            updatedAt: record.UpdatedAt,
        }));
    }
    catch (error) {
        logger_1.default.error('Error creating initial module sync records', {
            companyId,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to create module sync records: ${error.message}`);
    }
};
exports.createInitialModuleSyncRecords = createInitialModuleSyncRecords;
const getModuleSyncStatus = async (companyId) => {
    try {
        logger_1.default.debug('Fetching module sync status', { companyId });
        const syncRecords = await config_1.prisma.xeroModuleSync.findMany({
            where: { CompanyId: companyId },
            orderBy: { ModuleName: 'asc' },
        });
        return syncRecords.map(record => ({
            id: record.Id,
            companyId: record.CompanyId,
            moduleName: record.ModuleName,
            lastSyncTime: record.LastSyncTime,
            createdAt: record.CreatedAt,
            updatedAt: record.UpdatedAt,
        }));
    }
    catch (error) {
        logger_1.default.error('Error fetching module sync status', {
            companyId,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to fetch module sync status: ${error.message}`);
    }
};
exports.getModuleSyncStatus = getModuleSyncStatus;
const updateModuleSyncTime = async (companyId, moduleName, syncTime = new Date()) => {
    try {
        logger_1.default.debug('Updating module sync time', { companyId, moduleName, syncTime });
        const updatedRecord = await config_1.prisma.xeroModuleSync.update({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: moduleName,
                },
            },
            data: {
                LastSyncTime: syncTime,
            },
        });
        logger_1.default.info('Successfully updated module sync time', {
            companyId,
            moduleName,
            syncTime
        });
        return {
            id: updatedRecord.Id,
            companyId: updatedRecord.CompanyId,
            moduleName: updatedRecord.ModuleName,
            lastSyncTime: updatedRecord.LastSyncTime,
            createdAt: updatedRecord.CreatedAt,
            updatedAt: updatedRecord.UpdatedAt,
        };
    }
    catch (error) {
        logger_1.default.error('Error updating module sync time', {
            companyId,
            moduleName,
            syncTime,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to update module sync time: ${error.message}`);
    }
};
exports.updateModuleSyncTime = updateModuleSyncTime;
const getModuleSyncStatusByName = async (companyId, moduleName) => {
    try {
        logger_1.default.debug('Fetching module sync status by name', { companyId, moduleName });
        const syncRecord = await config_1.prisma.xeroModuleSync.findUnique({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: moduleName,
                },
            },
        });
        if (!syncRecord) {
            return null;
        }
        return {
            id: syncRecord.Id,
            companyId: syncRecord.CompanyId,
            moduleName: syncRecord.ModuleName,
            lastSyncTime: syncRecord.LastSyncTime,
            createdAt: syncRecord.CreatedAt,
            updatedAt: syncRecord.UpdatedAt,
        };
    }
    catch (error) {
        logger_1.default.error('Error fetching module sync status by name', {
            companyId,
            moduleName,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to fetch module sync status: ${error.message}`);
    }
};
exports.getModuleSyncStatusByName = getModuleSyncStatusByName;
const areAllModulesSynced = async (companyId) => {
    try {
        const syncRecords = await config_1.prisma.xeroModuleSync.findMany({
            where: { CompanyId: companyId },
        });
        const allSynced = syncRecords.every(record => record.LastSyncTime !== null);
        logger_1.default.debug('Checked if all modules are synced', {
            companyId,
            totalModules: syncRecords.length,
            allSynced
        });
        return allSynced;
    }
    catch (error) {
        logger_1.default.error('Error checking if all modules are synced', {
            companyId,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to check module sync status: ${error.message}`);
    }
};
exports.areAllModulesSynced = areAllModulesSynced;
//# sourceMappingURL=xeroModuleSync.service.js.map