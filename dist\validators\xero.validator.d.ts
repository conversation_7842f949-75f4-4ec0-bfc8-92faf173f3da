import { z } from 'zod';
export declare const xeroCallbackQuerySchema: z.ZodObject<{
    code: z.ZodEffects<z.ZodString, string, string>;
    state: z.ZodEffects<z.ZodString, string, string>;
}, "strip", z.ZodTypeAny, {
    code: string;
    state: string;
}, {
    code: string;
    state: string;
}>;
export declare const companyIdSchema: z.ZodObject<{
    companyId: z.ZodEffects<z.ZodString, string, string>;
}, "strip", z.ZodTypeAny, {
    companyId: string;
}, {
    companyId: string;
}>;
export declare const companyInfoSchema: z.ZodObject<{
    name: z.ZodEffects<z.ZodString, string, string>;
    financialYearEnd: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    financialYearEnd?: string | undefined;
}, {
    name: string;
    financialYearEnd?: string | undefined;
}>;
export type ValidatedXeroCallbackQuery = z.infer<typeof xeroCallbackQuerySchema>;
export type ValidatedCompanyId = z.infer<typeof companyIdSchema>;
export type ValidatedCompanyInfo = z.infer<typeof companyInfoSchema>;
//# sourceMappingURL=xero.validator.d.ts.map