/**
 * @fileoverview Main Express application configuration and middleware setup
 * @description This file configures the Express.js application with security middleware,
 * CORS settings, rate limiting, request logging, and route handling for the Furgal backend API.
 *
 * Key Features:
 * - Security headers with Helmet
 * - CORS configuration for cross-origin requests
 * - Rate limiting to prevent abuse
 * - Request/response logging
 * - Body parsing with size limits
 * - Centralized error handling
 * - Health check endpoints
 * - API versioning support
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';
import { config } from './config/config';
import { errorHandler, notFoundHandler } from './middlewares/error.middleware';
import { requestLogger } from './middlewares/requestLogger.middleware';
import healthRoutes from './routes/health.routes';
import logger from './utils/logger';
import routes from './routes';

/**
 * Creates and configures the Express application with all necessary middleware
 * @returns {express.Application} Configured Express application instance
 */
const createApp = (): express.Application => {
  const app = express();

  // Trust proxy for accurate IP addresses behind reverse proxy (load balancers, CDNs)
  app.set('trust proxy', 1);

  // Request logging middleware - different formats for development vs production
  if (config.IS_DEVELOPMENT) {
    // Development: Use concise colored output
    app.use(morgan('dev'));
  } else {
    // Production: Use combined format with structured logging
    app.use(
      morgan('combined', {
        stream: { write: (message: string) => logger.info(message.trim()) },
      })
    );
  }

  // Security middleware - Helmet sets various HTTP headers for security
  app.use(
    helmet({
      // Enable CSP only in production to avoid development issues
      contentSecurityPolicy: config.IS_PRODUCTION ? true : false,
      // Disable COEP as it can interfere with some frontend frameworks
      crossOriginEmbedderPolicy: false,
    })
  );

  // CORS (Cross-Origin Resource Sharing) configuration
  // Allows frontend applications to make requests to this API
  logger.info('CORS Configuration Applied', {
    origins: ['http://localhost:8080', 'http://localhost:3000'],
  });
  app.use(
    cors({
      // Allow requests from these origins
      origin: ['http://localhost:8080', 'http://localhost:3000'],
      // Allow cookies and authentication headers
      credentials: true,
      // Allowed HTTP methods
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      // Comprehensive list of allowed headers including custom headers
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Correlation-ID',
        'X-Request-ID',
        'X-Client-Version',
        'X-Client-Platform',
        'X-Forwarded-For',
        'Accept',
        'Accept-Language',
        'Accept-Encoding',
        'Origin',
        'X-Requested-With',
        'Cache-Control',
        'Pragma',
        'Referer',
        'User-Agent',
        'Sec-CH-UA',
        'Sec-CH-UA-Mobile',
        'Sec-CH-UA-Platform',
      ],
      // Headers exposed to the client
      exposedHeaders: ['X-Correlation-ID'],
      // Success status for preflight requests
      optionsSuccessStatus: 200,
    })
  );

  // Body parsing middleware with size limits for security
  app.use(
    express.json({
      limit: '10mb', // Limit JSON payload size
      verify: (req, _res, buf) => {
        // Store raw body for webhook verification if needed (e.g., Xero webhooks)
        (req as express.Request & { rawBody?: Buffer }).rawBody = buf;
      },
    })
  );
  // URL-encoded form data parsing
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Rate limiting middleware to prevent abuse and DoS attacks
  const limiter = rateLimit({
    windowMs: config.RATE_LIMIT.WINDOW_MS, // Time window in milliseconds
    max: config.RATE_LIMIT.MAX_REQUESTS, // Maximum requests per window
    message: {
      success: false,
      error: 'Too many requests from this IP, please try again later.',
      data: {
        retryAfter: Math.ceil(config.RATE_LIMIT.WINDOW_MS / 1000),
      },
    },
    standardHeaders: true, // Return rate limit info in headers
    legacyHeaders: false, // Disable legacy X-RateLimit-* headers
    skip: (req) => {
      // Skip rate limiting for health checks to avoid monitoring issues
      return req.path.startsWith('/health');
    },
  });
  // app.use(limiter);

  // Custom middleware for request logging and correlation
  app.use(requestLogger);

  // Health check routes (mounted before API prefix for direct access)
  app.use('/health', healthRoutes);

  // API routes with versioning (e.g., /api/v1)
  const apiPrefix = `${config.API.PREFIX}/${config.API.VERSION}`;
  app.use(`${apiPrefix}`, routes);

  // Handle 404 for unmatched routes - must be after all route definitions
  app.use(notFoundHandler);

  // Centralized error handling middleware - must be the last middleware
  app.use(errorHandler);

  return app;
};

/**
 * Create and export the configured Express application
 * This is the main application instance that will be used by the server
 */
const app = createApp();

export default app;
