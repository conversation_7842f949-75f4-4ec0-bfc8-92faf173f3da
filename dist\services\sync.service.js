"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSyncHistory = exports.triggerAllEntitiesSync = exports.triggerEntitySync = exports.getSyncStatus = exports.SYNC_ENTITIES = void 0;
const config_1 = require("@config/config");
const axiosClient_1 = __importDefault(require("@utils/axiosClient"));
const logger_1 = __importDefault(require("@utils/logger"));
const uuid_1 = require("uuid");
const error_middleware_1 = require("@middlewares/error.middleware");
const logApiCall_1 = require("@utils/logApiCall");
const sync_config_1 = require("@config/sync.config");
exports.SYNC_ENTITIES = [
    "Accounts",
    "BankTransactions",
    "BankTransfers",
    "Budgets",
    "Contacts",
    "CreditNotes",
    "Currencies",
    "Employees",
    "ExpenseClaims",
    "Invoices",
    "Journals",
    "ManualJournals",
    "Items",
    "Payments",
    "PurchaseOrders",
    "TaxRates",
    "TrackingCategories",
    "Attachments",
    "TrialBalance",
    "ProfitLoss",
    "BalanceSheet",
];
const getSyncStatus = async (userId, companyId) => {
    try {
        const company = await config_1.prisma.company.findFirst({
            where: { Id: companyId, UserId: userId },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
        }
        const syncStatuses = await Promise.all(exports.SYNC_ENTITIES.map(async (entity) => {
            const log = await config_1.prisma.syncLog.findFirst({
                where: { CompanyId: companyId, Entity: entity },
                orderBy: { StartedAt: "desc" },
            });
            let status = "NEVER_SYNCED";
            if (log?.Status) {
                switch (log.Status) {
                    case "PENDING":
                    case "RETRYING":
                        status = "PENDING";
                        break;
                    case "IN_PROGRESS":
                        status = "IN_PROGRESS";
                        break;
                    case "SUCCESS":
                    case "WARNING":
                        status = "COMPLETED";
                        break;
                    case "ERROR":
                    case "CANCELLED":
                        status = "FAILED";
                        break;
                    default:
                        status = "NEVER_SYNCED";
                }
            }
            return {
                entity,
                lastSync: log?.CompletedAt || log?.StartedAt || null,
                status,
                errorMessage: status === "FAILED" ? log?.Message || "" : "",
            };
        }));
        return {
            companyId,
            companyName: company.Name,
            connectionStatus: company.ConnectionStatus,
            entities: syncStatuses,
            lastUpdated: new Date(),
        };
    }
    catch (error) {
        logger_1.default.error("getSyncStatus failed", { userId, companyId, error });
        throw handleUnexpected(error, "SYNC_STATUS_RETRIEVAL_FAILED");
    }
};
exports.getSyncStatus = getSyncStatus;
const triggerEntitySync = async (userId, request) => {
    try {
        console.log("LAMBDA_SYNC_BANK_TRANSACTIONS_URL", request);
        const { entities, companyId, priority = "NORMAL", fullSync = false } = request;
        const company = await config_1.prisma.company.findFirst({
            where: { Id: companyId, UserId: userId },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
        }
        if (!company.XeroTenantId || company.ConnectionStatus !== "ACTIVE") {
            throw new error_middleware_1.BadRequestError("Company does not have an active Xero connection", "NO_ACTIVE_XERO_CONNECTION");
        }
        const triggerRecords = [];
        console.log("entities", entities);
        for (const entity of entities) {
            const requestId = (0, uuid_1.v4)();
            const startedAt = new Date();
            const lambdaUrl = sync_config_1.lambdaEntityUrlMap[entity];
            if (!lambdaUrl) {
                logger_1.default.warn(`No Lambda URL configured for entity: ${entity}`);
                continue;
            }
            try {
                const payload = { entity, companyId, fullSync, priority, requestId, dumpToDatabase: true };
                console.log(payload);
                console.log(lambdaUrl);
                return await axiosClient_1.default.post(lambdaUrl, payload);
            }
            catch (error) {
                const completedAt = new Date();
                const duration = `${completedAt.getTime() - startedAt.getTime()}ms`;
                await config_1.prisma.syncLog.upsert({
                    where: { Id: requestId },
                    update: {
                        Status: "ERROR",
                        CompletedAt: completedAt,
                        RetryCount: 1,
                        Message: error.message || "Failed",
                        ErrorDetails: error,
                        Duration: duration,
                        UpdatedAt: completedAt,
                    },
                    create: {
                        Id: requestId,
                        CompanyId: companyId,
                        UserId: userId,
                        Entity: entity,
                        Integration: "Xero",
                        Status: "ERROR",
                        RequestId: requestId,
                        StartedAt: startedAt,
                        CompletedAt: completedAt,
                        RequestPayload: { entity, companyId, fullSync, priority },
                        ApiEndpoint: lambdaUrl,
                        Method: "POST",
                        Message: error.message || "Unknown error",
                        ErrorDetails: error,
                        Duration: duration,
                        CreatedAt: startedAt,
                    },
                });
                await (0, logApiCall_1.logApiCall)({
                    companyId,
                    userId,
                    method: "POST",
                    apiUrl: lambdaUrl,
                    status: "500",
                    integrationName: "Xero",
                    apiName: `Sync ${entity}`,
                    requestPayload: { entity, companyId, fullSync, priority },
                    responsePayload: error.response?.data,
                    duration,
                });
                triggerRecords.push({
                    entity,
                    status: "FAILED",
                    requestId,
                });
            }
        }
        return {
            success: true,
            message: `Sync triggered for ${entities.length} entities`,
            data: {
                companyId,
                companyName: company.Name,
                triggerRecords,
                estimatedDuration: `${entities.length * 2}-${entities.length * 5} minutes`,
            },
        };
    }
    catch (error) {
        logger_1.default.error("triggerEntitySync failed", error);
        throw handleUnexpected(error, "ENTITY_SYNC_TRIGGER_FAILED");
    }
};
exports.triggerEntitySync = triggerEntitySync;
const triggerAllEntitiesSync = async (userId, companyId, options = {}) => {
    return (0, exports.triggerEntitySync)(userId, {
        entities: [...exports.SYNC_ENTITIES],
        companyId,
        ...(options.priority ? { priority: options.priority } : {}),
        ...(options.fullSync !== undefined ? { fullSync: options.fullSync } : {}),
    });
};
exports.triggerAllEntitiesSync = triggerAllEntitiesSync;
const getSyncHistory = async (userId, companyId, options = {}) => {
    const { limit = 50, offset = 0, entity, status, dateFrom, dateTo } = options;
    const company = await config_1.prisma.company.findFirst({
        where: { Id: companyId, UserId: userId },
    });
    if (!company) {
        throw new error_middleware_1.BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
    }
    const where = { CompanyId: companyId };
    if (entity) {
        where.Entity = entity;
    }
    if (status) {
        const statusMap = {
            PENDING: "PENDING",
            IN_PROGRESS: "IN_PROGRESS",
            COMPLETED: "SUCCESS",
            FAILED: "ERROR",
        };
        where.Status = statusMap[status] || status;
    }
    if (dateFrom || dateTo) {
        where.StartedAt = {};
        if (dateFrom)
            where.StartedAt.gte = dateFrom;
        if (dateTo)
            where.StartedAt.lte = dateTo;
    }
    const [syncLogs, totalCount] = await Promise.all([
        config_1.prisma.syncLog.findMany({
            where,
            orderBy: { StartedAt: "desc" },
            take: limit,
            skip: offset,
        }),
        config_1.prisma.syncLog.count({ where }),
    ]);
    const formatted = syncLogs.map((log) => ({
        id: log.Id,
        entity: log.Entity,
        status: log.Status,
        errorMessage: log.Message,
        duration: log.Duration,
        createdAt: log.CreatedAt,
        completedAt: log.CompletedAt,
    }));
    return {
        companyId,
        companyName: company.Name,
        history: formatted,
        pagination: {
            totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: Math.floor(offset / limit) + 1,
            limit,
            offset,
            hasNextPage: offset + limit < totalCount,
            hasPreviousPage: offset > 0,
        },
    };
};
exports.getSyncHistory = getSyncHistory;
const handleUnexpected = (error, code) => {
    if (error instanceof error_middleware_1.BadRequestError)
        return error;
    logger_1.default.error(`Unexpected error: ${error instanceof Error ? error.message : error}`);
    return new error_middleware_1.InternalServerError(error instanceof Error ? error.message : "Unknown error", code);
};
//# sourceMappingURL=sync.service.js.map