"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSyncHistory = exports.triggerAllEntitiesSync = exports.triggerEntitySync = exports.getSyncStatus = exports.SYNC_ENTITIES = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
exports.SYNC_ENTITIES = [
    'Accounts',
    'BankTransactions',
    'BankTransfers',
    'Budgets',
    'Contacts',
    'CreditNotes',
    'Currencies',
    'Employees',
    'ExpenseClaims',
    'Invoices',
    'Journals',
    'ManualJournals',
    'Items',
    'Payments',
    'PurchaseOrders',
    'TaxRates',
    'TrackingCategories',
    'Attachments',
    'Reports',
];
const getSyncStatus = async (userId, companyId) => {
    try {
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const syncStatuses = [];
        for (const entity of exports.SYNC_ENTITIES) {
            try {
                const lastSync = await config_1.prisma.syncLog.findFirst({
                    where: {
                        CompanyId: companyId,
                        Entity: entity,
                    },
                    orderBy: {
                        StartedAt: 'desc',
                    },
                    select: {
                        StartedAt: true,
                        CompletedAt: true,
                        Status: true,
                        Message: true,
                        Duration: true,
                    },
                });
                let status = 'NEVER_SYNCED';
                if (lastSync?.Status) {
                    const dbStatus = lastSync.Status;
                    switch (dbStatus) {
                        case 'PENDING':
                        case 'RETRYING':
                            status = 'PENDING';
                            break;
                        case 'IN_PROGRESS':
                            status = 'IN_PROGRESS';
                            break;
                        case 'SUCCESS':
                        case 'WARNING':
                            status = 'COMPLETED';
                            break;
                        case 'ERROR':
                        case 'CANCELLED':
                            status = 'FAILED';
                            break;
                        default:
                            status = 'NEVER_SYNCED';
                    }
                }
                syncStatuses.push({
                    entity,
                    lastSync: lastSync?.CompletedAt || lastSync?.StartedAt || null,
                    status,
                    recordCount: 0,
                    errorMessage: status === 'FAILED' ? lastSync?.Message || '' : '',
                });
            }
            catch (error) {
                syncStatuses.push({
                    entity,
                    lastSync: null,
                    status: 'NEVER_SYNCED',
                    recordCount: 0,
                });
            }
        }
        logger_1.default.info('Sync status retrieved successfully', {
            userId,
            companyId,
            entitiesCount: syncStatuses.length,
        });
        return {
            companyId,
            companyName: company.Name,
            connectionStatus: company.ConnectionStatus,
            entities: syncStatuses,
            lastUpdated: new Date(),
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve sync status', {
            userId,
            companyId,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve sync status: ${errorMessage}`, 'SYNC_STATUS_RETRIEVAL_FAILED');
    }
};
exports.getSyncStatus = getSyncStatus;
const triggerEntitySync = async (userId, request) => {
    try {
        const { entities, companyId, priority = 'NORMAL', fullSync = false } = request;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        if (!company.XeroTenantId || company.ConnectionStatus !== 'ACTIVE') {
            throw new error_middleware_1.BadRequestError('Company does not have an active Xero connection', 'NO_ACTIVE_XERO_CONNECTION');
        }
        const invalidEntities = entities.filter((entity) => !exports.SYNC_ENTITIES.includes(entity));
        if (invalidEntities.length > 0) {
            throw new error_middleware_1.BadRequestError(`Invalid entities: ${invalidEntities.join(', ')}`, 'INVALID_ENTITIES');
        }
        const triggerRecords = [];
        for (const entity of entities) {
            const triggerRecord = {
                Id: `trigger-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                CompanyId: companyId,
                EntityType: entity,
                Priority: priority,
                FullSync: fullSync,
                Status: 'PENDING',
                TriggeredBy: userId,
                CreatedAt: new Date(),
            };
            triggerRecords.push(triggerRecord);
        }
        logger_1.default.info('Sync triggers created successfully', {
            userId,
            companyId,
            entities,
            priority,
            fullSync,
            triggerIds: triggerRecords.map((r) => r.Id),
        });
        return {
            success: true,
            message: `Sync triggered for ${entities.length} entities`,
            data: {
                companyId,
                companyName: company.Name,
                entities,
                priority,
                fullSync,
                triggerIds: triggerRecords.map((r) => r.Id),
                estimatedDuration: `${entities.length * 2}-${entities.length * 5} minutes`,
            },
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to trigger entity sync', {
            userId,
            request,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to trigger sync: ${errorMessage}`, 'SYNC_TRIGGER_FAILED');
    }
};
exports.triggerEntitySync = triggerEntitySync;
const triggerAllEntitiesSync = async (userId, companyId, options = {}) => {
    return (0, exports.triggerEntitySync)(userId, {
        entities: [...exports.SYNC_ENTITIES],
        companyId,
        priority: options.priority || 'NORMAL',
        fullSync: options.fullSync || false,
    });
};
exports.triggerAllEntitiesSync = triggerAllEntitiesSync;
const getSyncHistory = async (userId, companyId, options = {}) => {
    try {
        const { limit = 50, offset = 0, entity, status, dateFrom, dateTo } = options;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const whereConditions = {
            CompanyId: companyId,
        };
        if (entity) {
            whereConditions.Entity = entity;
        }
        if (status) {
            const statusMap = {
                PENDING: 'PENDING',
                IN_PROGRESS: 'IN_PROGRESS',
                COMPLETED: 'SUCCESS',
                FAILED: 'ERROR',
            };
            whereConditions.Status = statusMap[status] || status;
        }
        if (dateFrom || dateTo) {
            whereConditions.StartedAt = {};
            if (dateFrom) {
                whereConditions.StartedAt.gte = dateFrom;
            }
            if (dateTo) {
                whereConditions.StartedAt.lte = dateTo;
            }
        }
        const [syncHistory, totalCount] = await Promise.all([
            config_1.prisma.syncLog.findMany({
                where: whereConditions,
                orderBy: {
                    StartedAt: 'desc',
                },
                take: limit,
                skip: offset,
                select: {
                    Id: true,
                    Entity: true,
                    Status: true,
                    Message: true,
                    Duration: true,
                    StartedAt: true,
                    CompletedAt: true,
                    CreatedAt: true,
                },
            }),
            config_1.prisma.syncLog.count({
                where: whereConditions,
            }),
        ]);
        const formattedHistory = syncHistory.map((record) => ({
            id: record.Id,
            entity: record.Entity,
            status: record.Status,
            recordsProcessed: 0,
            recordsSucceeded: 0,
            recordsFailed: 0,
            errorMessage: record.Message || undefined,
            duration: record.Duration || undefined,
            createdAt: record.CreatedAt,
            completedAt: record.CompletedAt || undefined,
        }));
        logger_1.default.info('Sync history retrieved successfully', {
            userId,
            companyId,
            totalCount,
            returnedCount: syncHistory.length,
        });
        return {
            companyId,
            companyName: company.Name,
            history: formattedHistory,
            pagination: {
                totalCount,
                totalPages: Math.ceil(totalCount / limit),
                currentPage: Math.floor(offset / limit) + 1,
                limit,
                offset,
                hasNextPage: offset + limit < totalCount,
                hasPreviousPage: offset > 0,
            },
            appliedFilters: {
                entity,
                status,
                dateFrom,
                dateTo,
            },
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve sync history', {
            userId,
            companyId,
            options,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve sync history: ${errorMessage}`, 'SYNC_HISTORY_RETRIEVAL_FAILED');
    }
};
exports.getSyncHistory = getSyncHistory;
//# sourceMappingURL=sync.service.js.map