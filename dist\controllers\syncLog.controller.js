"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSyncStatsController = exports.retrySyncController = exports.getSyncLogDetailsController = exports.getSyncLogsController = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const syncLog_service_1 = require("@services/syncLog.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const syncLogQuerySchema = zod_1.z.object({
    startDate: zod_1.z.string().datetime().optional(),
    endDate: zod_1.z.string().datetime().optional(),
    entity: zod_1.z.string().optional(),
    status: zod_1.z.nativeEnum(client_1.SyncStatus).optional(),
    integration: zod_1.z.string().optional(),
    limit: zod_1.z.coerce.number().min(1).max(100).default(50),
    offset: zod_1.z.coerce.number().min(0).default(0),
});
const companyIdSchema = zod_1.z.object({
    companyId: zod_1.z.string().uuid(),
});
const syncLogIdSchema = zod_1.z.object({
    syncLogId: zod_1.z.string().uuid(),
});
const getSyncLogsHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const companyValidation = companyIdSchema.safeParse(req.params);
        if (!companyValidation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid company ID',
                data: {
                    validationErrors: companyValidation.error.errors,
                },
            });
        }
        const queryValidation = syncLogQuerySchema.safeParse(req.query);
        if (!queryValidation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: {
                    validationErrors: queryValidation.error.errors,
                },
            });
        }
        const { companyId } = companyValidation.data;
        const filters = {
            ...queryValidation.data,
            startDate: queryValidation.data.startDate
                ? new Date(queryValidation.data.startDate)
                : undefined,
            endDate: queryValidation.data.endDate ? new Date(queryValidation.data.endDate) : undefined,
        };
        const result = await (0, syncLog_service_1.getSyncLogs)(user.userId, companyId, filters);
        res.status(200).json((0, response_1.successResponse)('Sync logs retrieved successfully', {
            syncLogs: result.syncLogs,
            pagination: result.pagination,
            appliedFilters: result.filters,
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncLogsController = (0, error_middleware_1.asyncErrorHandler)(getSyncLogsHandler);
const getSyncLogDetailsHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validation = syncLogIdSchema.safeParse(req.params);
        if (!validation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid sync log ID',
                data: {
                    validationErrors: validation.error.errors,
                },
            });
        }
        const { syncLogId } = validation.data;
        const syncLog = await (0, syncLog_service_1.getSyncLogById)(user.userId, syncLogId);
        res.status(200).json((0, response_1.successResponse)('Sync log details retrieved successfully', {
            syncLog,
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncLogDetailsController = (0, error_middleware_1.asyncErrorHandler)(getSyncLogDetailsHandler);
const retrySyncHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validation = syncLogIdSchema.safeParse(req.params);
        if (!validation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid sync log ID',
                data: {
                    validationErrors: validation.error.errors,
                },
            });
        }
        const { syncLogId } = validation.data;
        const result = await (0, syncLog_service_1.retrySyncOperation)(user.userId, syncLogId);
        res.status(200).json((0, response_1.successResponse)('Sync retry initiated successfully', result.data));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.retrySyncController = (0, error_middleware_1.asyncErrorHandler)(retrySyncHandler);
const getSyncStatsHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validation = companyIdSchema.safeParse(req.params);
        if (!validation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid company ID',
                data: {
                    validationErrors: validation.error.errors,
                },
            });
        }
        const { companyId } = validation.data;
        const result = await (0, syncLog_service_1.getSyncLogs)(user.userId, companyId, {
            limit: 100,
            offset: 0,
        });
        const stats = {
            totalSyncs: result.pagination.totalCount,
            recentSyncs: result.syncLogs.length,
            statusBreakdown: {
                SUCCESS: 0,
                ERROR: 0,
                WARNING: 0,
                PENDING: 0,
                IN_PROGRESS: 0,
                RETRYING: 0,
                CANCELLED: 0,
            },
            entityBreakdown: {},
            averageDuration: 0,
            successRate: 0,
        };
        let totalDurationMs = 0;
        let durationsCount = 0;
        result.syncLogs.forEach((log) => {
            stats.statusBreakdown[log.Status]++;
            stats.entityBreakdown[log.Entity] = (stats.entityBreakdown[log.Entity] || 0) + 1;
            if (log.Duration) {
                const durationMatch = log.Duration.match(/(\d+\.?\d*)/);
                if (durationMatch && durationMatch[1] !== undefined) {
                    totalDurationMs += parseFloat(durationMatch[1]);
                    durationsCount++;
                }
            }
        });
        if (durationsCount > 0) {
            stats.averageDuration = totalDurationMs / durationsCount;
        }
        if (result.syncLogs.length > 0) {
            stats.successRate = (stats.statusBreakdown.SUCCESS / result.syncLogs.length) * 100;
        }
        res.status(200).json((0, response_1.successResponse)('Sync statistics retrieved successfully', {
            companyId,
            statistics: stats,
            generatedAt: new Date(),
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncStatsController = (0, error_middleware_1.asyncErrorHandler)(getSyncStatsHandler);
//# sourceMappingURL=syncLog.controller.js.map