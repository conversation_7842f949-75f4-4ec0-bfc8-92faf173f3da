"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reconnectCompanyXeroController = exports.disconnectCompanyXeroController = exports.getCompanyByIdController = exports.getCompanyStats = exports.getCompanies = void 0;
const xero_service_1 = require("@services/xero.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const company_validator_1 = require("@validators/company.validator");
const getCompaniesHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = company_validator_1.companyListQuerySchema.safeParse(req.query);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const filters = validationResult.data;
        const result = await (0, xero_service_1.getCompaniesWithFilters)(user.userId, filters);
        res.status(200).json((0, response_1.successResponse)('Companies retrieved successfully', {
            companies: result.companies,
            pagination: result.pagination,
            appliedFilters: result.filters,
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getCompanies = (0, error_middleware_1.asyncErrorHandler)(getCompaniesHandler);
const getCompanyStatsHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const allCompanies = await (0, xero_service_1.getCompaniesWithFilters)(user.userId, { limit: 1000 });
        const stats = {
            totalCompanies: allCompanies.pagination.totalCount,
            connectionStatusBreakdown: {
                ACTIVE: 0,
                EXPIRED: 0,
                DISCONNECTED: 0,
                PENDING: 0,
            },
            withXeroConnection: 0,
            withoutXeroConnection: 0,
            recentlyCreated: 0,
        };
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        allCompanies.companies.forEach((company) => {
            stats.connectionStatusBreakdown[company.ConnectionStatus]++;
            if (company.XeroTenantId) {
                stats.withXeroConnection++;
            }
            else {
                stats.withoutXeroConnection++;
            }
            if (company.CreatedAt > thirtyDaysAgo) {
                stats.recentlyCreated++;
            }
        });
        res.status(200).json((0, response_1.successResponse)('Company statistics retrieved successfully', stats));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getCompanyStats = (0, error_middleware_1.asyncErrorHandler)(getCompanyStatsHandler);
const getCompanyByIdHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = company_validator_1.companyIdSchema.safeParse(req.params);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid company ID',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const { id: companyId } = validationResult.data;
        const company = await (0, xero_service_1.getCompanyById)(user.userId, companyId);
        if (!company) {
            res.status(404).json({
                success: false,
                error: 'Company not found',
                data: {
                    companyId,
                    message: 'Company not found or you do not have access to it',
                },
            });
            return;
        }
        res.status(200).json((0, response_1.successResponse)('Company retrieved successfully', {
            company,
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getCompanyByIdController = (0, error_middleware_1.asyncErrorHandler)(getCompanyByIdHandler);
const disconnectCompanyXeroHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = company_validator_1.companyIdSchema.safeParse(req.params);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid company ID',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const { id: companyId } = validationResult.data;
        const updatedCompany = await (0, xero_service_1.disconnectCompanyXero)(user.userId, companyId);
        res.status(200).json((0, response_1.successResponse)('Xero connection disconnected successfully', {
            company: updatedCompany,
            message: 'Company has been disconnected from Xero. All Xero tokens have been removed.',
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.disconnectCompanyXeroController = (0, error_middleware_1.asyncErrorHandler)(disconnectCompanyXeroHandler);
const reconnectCompanyXeroHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = company_validator_1.companyIdSchema.safeParse(req.params);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid company ID',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const { id: companyId } = validationResult.data;
        const reconnectionData = await (0, xero_service_1.reconnectCompanyXero)(user.userId, companyId);
        res.status(200).json((0, response_1.successResponse)('Xero reconnection initiated successfully', {
            authorizationUrl: reconnectionData.authorizationUrl,
            companyId,
            message: 'Please visit the authorization URL to reconnect your Xero account.',
            instructions: 'After completing the authorization, your company will be reconnected to Xero.',
        }));
        return;
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.reconnectCompanyXeroController = (0, error_middleware_1.asyncErrorHandler)(reconnectCompanyXeroHandler);
//# sourceMappingURL=company.controller.js.map