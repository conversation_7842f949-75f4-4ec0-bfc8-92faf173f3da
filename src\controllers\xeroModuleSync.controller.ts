import { Request, Response, NextFunction } from 'express';
import {
  getModuleSyncStatus,
  getModuleSyncStatusByName,
  updateModuleSyncTime,
  areAllModulesSynced,
  XERO_MODULES,
  XeroModuleName,
} from '@services/xeroModuleSync.service';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';
import { BadRequestError } from '@middlewares/error.middleware';

// Extend Express Request interface to include 'user'
declare module 'express-serve-static-core' {
  interface Request {
    user?: any;
  }
}

/**
 * Controller: Get module sync status for a company
 * GET /companies/:companyId/xero/modules/sync-status
 */
const getModuleSyncStatusHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId } = req.params;
    // const user: any = req.user; // TODO: Add proper authorization

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
    }

    // Verify user has access to this company
    // This could be enhanced with proper authorization middleware
    const moduleSyncStatus = await getModuleSyncStatus(companyId);

    // Add summary information
    const totalModules = XERO_MODULES.length;
    const syncedModules = moduleSyncStatus.filter(module => module.lastSyncTime !== null).length;
    const pendingModules = totalModules - syncedModules;
    const allSynced = await areAllModulesSynced(companyId);

    const responseData = {
      companyId,
      modules: moduleSyncStatus,
      summary: {
        totalModules,
        syncedModules,
        pendingModules,
        allSynced,
        syncProgress: totalModules > 0 ? Math.round((syncedModules / totalModules) * 100) : 0,
      },
    };

    res.status(200).json(
      successResponse('Module sync status retrieved successfully', responseData)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getModuleSyncStatusController = asyncErrorHandler(getModuleSyncStatusHandler);

/**
 * Controller: Get sync status for a specific module
 * GET /companies/:companyId/xero/modules/:moduleName/sync-status
 */
const getSpecificModuleSyncStatusHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId, moduleName } = req.params;
    // const user: any = req.user; // TODO: Add proper authorization

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
    }

    if (!moduleName) {
      throw new BadRequestError('Module name is required', 'MISSING_MODULE_NAME');
    }

    // Validate module name
    if (!XERO_MODULES.includes(moduleName as XeroModuleName)) {
      throw new BadRequestError(
        `Invalid module name. Valid modules are: ${XERO_MODULES.join(', ')}`,
        'INVALID_MODULE_NAME'
      );
    }

    const moduleSyncStatus = await getModuleSyncStatusByName(companyId, moduleName as XeroModuleName);

    if (!moduleSyncStatus) {
      throw new BadRequestError(
        'Module sync status not found. Company may not be connected to Xero.',
        'MODULE_SYNC_NOT_FOUND'
      );
    }

    res.status(200).json(
      successResponse('Module sync status retrieved successfully', moduleSyncStatus)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getSpecificModuleSyncStatusController = asyncErrorHandler(getSpecificModuleSyncStatusHandler);

/**
 * Controller: Update sync time for a specific module (for testing/manual updates)
 * PUT /companies/:companyId/xero/modules/:moduleName/sync-time
 */
const updateModuleSyncTimeHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId, moduleName } = req.params;
    const { syncTime } = req.body;
    // const user: any = req.user; // TODO: Add proper authorization

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
    }

    if (!moduleName) {
      throw new BadRequestError('Module name is required', 'MISSING_MODULE_NAME');
    }

    // Validate module name
    if (!XERO_MODULES.includes(moduleName as XeroModuleName)) {
      throw new BadRequestError(
        `Invalid module name. Valid modules are: ${XERO_MODULES.join(', ')}`,
        'INVALID_MODULE_NAME'
      );
    }

    // Parse sync time if provided, otherwise use current time
    let parsedSyncTime: Date;
    if (syncTime) {
      parsedSyncTime = new Date(syncTime);
      if (isNaN(parsedSyncTime.getTime())) {
        throw new BadRequestError('Invalid sync time format', 'INVALID_SYNC_TIME');
      }
    } else {
      parsedSyncTime = new Date();
    }

    const updatedModuleSync = await updateModuleSyncTime(
      companyId,
      moduleName as XeroModuleName,
      parsedSyncTime
    );

    res.status(200).json(
      successResponse('Module sync time updated successfully', updatedModuleSync)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const updateModuleSyncTimeController = asyncErrorHandler(updateModuleSyncTimeHandler);

/**
 * Controller: Get available Xero modules
 * GET /xero/modules
 */
const getAvailableModulesHandler = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const responseData = {
      modules: XERO_MODULES,
      totalCount: XERO_MODULES.length,
    };

    res.status(200).json(
      successResponse('Available Xero modules retrieved successfully', responseData)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getAvailableModulesController = asyncErrorHandler(getAvailableModulesHandler);

/**
 * Controller: Get sync summary for a company
 * GET /companies/:companyId/xero/sync-summary
 */
const getSyncSummaryHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId } = req.params;
    // const user: any = req.user; // TODO: Add proper authorization

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
    }

    const moduleSyncStatus = await getModuleSyncStatus(companyId);
    const allSynced = await areAllModulesSynced(companyId);

    const totalModules = XERO_MODULES.length;
    const syncedModules = moduleSyncStatus.filter(module => module.lastSyncTime !== null).length;
    const pendingModules = totalModules - syncedModules;

    // Get last sync times
    const lastSyncTimes = moduleSyncStatus
      .filter(module => module.lastSyncTime !== null)
      .map(module => module.lastSyncTime!)
      .sort((a, b) => b.getTime() - a.getTime());

    const mostRecentSync = lastSyncTimes.length > 0 ? lastSyncTimes[0] : null;
    const oldestSync = lastSyncTimes.length > 0 ? lastSyncTimes[lastSyncTimes.length - 1] : null;

    const responseData = {
      companyId,
      summary: {
        totalModules,
        syncedModules,
        pendingModules,
        allSynced,
        syncProgress: totalModules > 0 ? Math.round((syncedModules / totalModules) * 100) : 0,
        mostRecentSync,
        oldestSync,
      },
      pendingModules: moduleSyncStatus
        .filter(module => module.lastSyncTime === null)
        .map(module => module.moduleName),
      syncedModules: moduleSyncStatus
        .filter(module => module.lastSyncTime !== null)
        .map(module => ({
          moduleName: module.moduleName,
          lastSyncTime: module.lastSyncTime,
        }))
        .sort((a, b) => b.lastSyncTime!.getTime() - a.lastSyncTime!.getTime()),
    };

    res.status(200).json(
      successResponse('Sync summary retrieved successfully', responseData)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncSummaryController = asyncErrorHandler(getSyncSummaryHandler);
