"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshXeroToken = exports.xeroDisconnect = exports.xeroCallback = exports.generateAuthUrl = void 0;
const xero_service_1 = require("@services/xero.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const generateAuthUrlHandler = async (_req, res, next) => {
    try {
        const url = (0, xero_service_1.getXeroAuthUrl)();
        res.status(200).json((0, response_1.successResponse)('Xero authorization URL generated', { url }));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.generateAuthUrl = (0, error_middleware_1.asyncErrorHandler)(generateAuthUrlHandler);
const xeroCallbackHandler = async (req, res, next) => {
    try {
        const code = req.query['code'];
        const companyId = req.query['state'];
        if (!code || !companyId) {
            throw new Error('Missing required OAuth2 parameters.');
        }
        const user = req.user;
        const updatedCompany = await (0, xero_service_1.handleXeroCallback)(code, user.userId);
        res.status(200).json((0, response_1.successResponse)('Xero authorization successful', updatedCompany));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.xeroCallback = (0, error_middleware_1.asyncErrorHandler)(xeroCallbackHandler);
const disconnectHandler = async (req, res, next) => {
    try {
        const { companyId } = req.body;
        if (!companyId) {
            throw new Error('companyId is required in the request body.');
        }
        await (0, xero_service_1.disconnectXero)(companyId);
        res.status(200).json((0, response_1.successResponse)('Xero account disconnected successfully', null));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.xeroDisconnect = (0, error_middleware_1.asyncErrorHandler)(disconnectHandler);
const refreshXeroTokenHandler = async (req, res, next) => {
    try {
        const { companyId } = req.body;
        if (!companyId) {
            throw new Error('Missing companyId in request body.');
        }
        const updatedCompany = await (0, xero_service_1.refreshXeroTokens)(companyId);
        res.status(200).json((0, response_1.successResponse)('Xero tokens refreshed successfully', updatedCompany));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.refreshXeroToken = (0, error_middleware_1.asyncErrorHandler)(refreshXeroTokenHandler);
//# sourceMappingURL=xero.controller.js.map