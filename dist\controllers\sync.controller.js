"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSupportedEntitiesController = exports.getSyncHistoryController = exports.triggerAllSyncController = exports.triggerSyncController = exports.getSyncStatusController = void 0;
const sync_service_1 = require("@services/sync.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const sync_validator_1 = require("@validators/sync.validator");
const getSyncStatusHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = sync_validator_1.syncStatusQuerySchema.safeParse(req.query);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const { companyId } = validationResult.data;
        const syncStatus = await (0, sync_service_1.getSyncStatus)(user.userId, companyId);
        res.status(200).json((0, response_1.successResponse)('Sync status retrieved successfully', syncStatus));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncStatusController = (0, error_middleware_1.asyncErrorHandler)(getSyncStatusHandler);
const triggerSyncHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = sync_validator_1.syncTriggerSchema.safeParse(req.body);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid request data',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const syncRequest = validationResult.data;
        const result = await (0, sync_service_1.triggerEntitySync)(user.userId, syncRequest);
        res.status(200).json((0, response_1.successResponse)('Sync triggered successfully', result.data));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.triggerSyncController = (0, error_middleware_1.asyncErrorHandler)(triggerSyncHandler);
const triggerAllSyncHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const { companyId, priority = 'NORMAL', fullSync = false } = req.body;
        if (!companyId || typeof companyId !== 'string') {
            res.status(400).json({
                success: false,
                error: 'Company ID is required',
                data: {
                    message: 'companyId must be a valid UUID string',
                },
            });
            return;
        }
        const result = await (0, sync_service_1.triggerAllEntitiesSync)(user.userId, companyId, {
            priority,
            fullSync,
        });
        res.status(200).json((0, response_1.successResponse)('Full sync triggered successfully', result.data));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.triggerAllSyncController = (0, error_middleware_1.asyncErrorHandler)(triggerAllSyncHandler);
const getSyncHistoryHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validationResult = sync_validator_1.syncHistoryQuerySchema.safeParse(req.query);
        if (!validationResult.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: {
                    validationErrors: validationResult.error.errors,
                },
            });
            return;
        }
        const { companyId, ...options } = validationResult.data;
        const syncHistory = await (0, sync_service_1.getSyncHistory)(user.userId, companyId, {
            limit: options.limit,
            offset: options.offset,
            entity: options.entity,
            status: options.status,
            dateFrom: options.dateFrom,
            dateTo: options.dateTo,
        });
        res.status(200).json((0, response_1.successResponse)('Sync history retrieved successfully', syncHistory));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncHistoryController = (0, error_middleware_1.asyncErrorHandler)(getSyncHistoryHandler);
const getSupportedEntitiesHandler = async (_req, res, next) => {
    try {
        const entities = sync_service_1.SYNC_ENTITIES.map((entity) => ({
            name: entity,
            displayName: entity.replace(/([A-Z])/g, ' $1').trim(),
            description: getEntityDescription(entity),
        }));
        res.status(200).json((0, response_1.successResponse)('Supported entities retrieved successfully', {
            entities,
            totalCount: entities.length,
        }));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSupportedEntitiesController = (0, error_middleware_1.asyncErrorHandler)(getSupportedEntitiesHandler);
function getEntityDescription(entity) {
    const descriptions = {
        Accounts: 'Chart of accounts and account balances',
        BankTransactions: 'Bank transactions and reconciliation data',
        BankTransfers: 'Transfers between bank accounts',
        Budgets: 'Budget data and forecasting information',
        Contacts: 'Customers, suppliers, and other contacts',
        CreditNotes: 'Credit notes and refund information',
        Currencies: 'Currency rates and multi-currency data',
        Employees: 'Employee records and payroll information',
        ExpenseClaims: 'Expense claims and reimbursement requests',
        Invoices: 'Sales and purchase invoices',
        Journals: 'Journal entries and accounting transactions',
        ManualJournals: 'Manual journal entries and adjustments',
        Items: 'Inventory items and product catalog',
        Payments: 'Payment records and transaction history',
        PurchaseOrders: 'Purchase orders and procurement data',
        TaxRates: 'Tax rates and tax configuration',
        TrackingCategories: 'Tracking categories for reporting',
        Attachments: 'Document attachments and file uploads',
        Reports: 'Financial reports (P&L, Balance Sheet, Trial Balance)',
    };
    return descriptions[entity] || 'Xero entity synchronization';
}
//# sourceMappingURL=sync.controller.js.map