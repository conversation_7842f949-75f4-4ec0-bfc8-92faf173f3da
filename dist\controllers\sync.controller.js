"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSupportedEntitiesController = exports.getSyncHistoryController = exports.triggerAllSyncController = exports.triggerSyncController = exports.getSyncStatusController = void 0;
const sync_service_1 = require("@services/sync.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const sync_validator_1 = require("@validators/sync.validator");
const getSyncStatusHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validated = sync_validator_1.syncStatusQuerySchema.safeParse(req.query);
        if (!validated.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: validated.error.format(),
            });
            return;
        }
        const { companyId } = validated.data;
        const status = await (0, sync_service_1.getSyncStatus)(user.userId, companyId);
        res.status(200).json((0, response_1.successResponse)('Sync status retrieved successfully', status));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncStatusController = (0, error_middleware_1.asyncErrorHandler)(getSyncStatusHandler);
const triggerSyncHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validated = sync_validator_1.syncTriggerSchema.safeParse(req.body);
        if (!validated.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid request data',
                data: validated.error.format(),
            });
            return;
        }
        const result = await (0, sync_service_1.triggerEntitySync)(user.userId, validated.data);
        res.status(200).json((0, response_1.successResponse)('Sync triggered successfully', result.data));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.triggerSyncController = (0, error_middleware_1.asyncErrorHandler)(triggerSyncHandler);
const triggerAllSyncHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const { companyId, priority = 'NORMAL', fullSync = false } = req.body;
        if (!companyId || typeof companyId !== 'string') {
            res.status(400).json({
                success: false,
                error: 'Company ID is required',
            });
            return;
        }
        const result = await (0, sync_service_1.triggerAllEntitiesSync)(user.userId, companyId, {
            priority,
            fullSync,
        });
        res.status(200).json((0, response_1.successResponse)('Full sync triggered successfully', result.data));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.triggerAllSyncController = (0, error_middleware_1.asyncErrorHandler)(triggerAllSyncHandler);
const getSyncHistoryHandler = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user?.userId) {
            throw new Error('User authentication required');
        }
        const validated = sync_validator_1.syncHistoryQuerySchema.safeParse(req.query);
        if (!validated.success) {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                data: validated.error.format(),
            });
            return;
        }
        const { companyId, ...options } = validated.data;
        const history = await (0, sync_service_1.getSyncHistory)(user.userId, companyId, {
            limit: options.limit,
            offset: options.offset,
            entity: options.entity,
            ...(options.status !== undefined ? { status: options.status } : {}),
            ...(options.dateFrom !== undefined ? { dateFrom: options.dateFrom } : {}),
            ...(options.dateTo !== undefined ? { dateTo: options.dateTo } : {}),
        });
        res.status(200).json((0, response_1.successResponse)('Sync history retrieved successfully', history));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncHistoryController = (0, error_middleware_1.asyncErrorHandler)(getSyncHistoryHandler);
const getSupportedEntitiesHandler = async (_req, res, next) => {
    try {
        const entities = sync_service_1.SYNC_ENTITIES.map((entity) => ({
            name: entity,
            displayName: entity.replace(/([A-Z])/g, ' $1').trim(),
            description: getEntityDescription(entity),
        }));
        res.status(200).json((0, response_1.successResponse)('Supported entities retrieved successfully', {
            entities,
            totalCount: entities.length,
        }));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSupportedEntitiesController = (0, error_middleware_1.asyncErrorHandler)(getSupportedEntitiesHandler);
function getEntityDescription(entity) {
    const descriptions = {
        Accounts: 'Chart of accounts and balances',
        BankTransactions: 'Bank transaction records',
        BankTransfers: 'Bank transfer entries',
        Budgets: 'Financial budgets',
        Contacts: 'Customers and suppliers',
        CreditNotes: 'Issued credit notes',
        Currencies: 'Currency exchange rates',
        Employees: 'Employee details',
        ExpenseClaims: 'Employee expense claims',
        Invoices: 'Customer and supplier invoices',
        Journals: 'Journal entries',
        ManualJournals: 'Manual journal adjustments',
        Items: 'Product/service catalog',
        Payments: 'Payment transactions',
        PurchaseOrders: 'Purchase orders',
        TaxRates: 'Tax rates and codes',
        TrackingCategories: 'Tracking categories',
        Attachments: 'File and document attachments',
        TrialBalance: 'TrialBalance',
        ProfitLoss: 'ProfitLoss',
        BalanceSheet: 'BalanceSheet'
    };
    return descriptions[entity] || 'Xero entity';
}
//# sourceMappingURL=sync.controller.js.map