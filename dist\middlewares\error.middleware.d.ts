import { NextFunction, Request, Response } from 'express';
export interface AppError extends Error {
    statusCode?: number;
    isOperational?: boolean;
    code?: string;
    details?: any;
}
export declare class CustomError extends Error implements AppError {
    statusCode: number;
    isOperational: boolean;
    code?: string;
    details?: any;
    constructor(message: string, statusCode?: number, isOperational?: boolean, code?: string, details?: any);
}
export declare class BadRequestError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class UnauthorizedError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class XeroUnauthorizedError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class ForbiddenError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class NotFoundError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class ConflictError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare class ValidationError extends CustomError {
    constructor(message?: string, details?: any);
}
export declare class InternalServerError extends CustomError {
    constructor(message?: string, code?: string, details?: any);
}
export declare const errorHandler: (err: AppError, req: Request, res: Response, _next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, _res: Response, next: NextFunction) => void;
export declare const asyncErrorHandler: (fn: (req: Request, res: Response, next: NextFunction) => Promise<any> | any) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=error.middleware.d.ts.map