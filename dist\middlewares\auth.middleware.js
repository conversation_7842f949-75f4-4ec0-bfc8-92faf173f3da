"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const error_middleware_1 = require("./error.middleware");
const config_1 = require("../config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const authenticate = (req, _res, next) => {
    try {
        const authHeader = req.headers.authorization;
        logger_1.default.debug('Processing authentication header', {
            hasAuthHeader: !!authHeader,
            authHeaderPrefix: `${authHeader?.substring(0, 10)}...`,
        });
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new error_middleware_1.UnauthorizedError('Authorization token is required', 'MISSING_TOKEN');
        }
        const token = authHeader.split(' ')[1];
        if (!token) {
            throw new error_middleware_1.UnauthorizedError('Authorization token is required', 'MISSING_TOKEN');
        }
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.JWT.SECRET);
        req.user = decoded;
        next();
    }
    catch (error) {
        if (error instanceof error_middleware_1.UnauthorizedError) {
            next(error);
        }
        else {
            next(new error_middleware_1.UnauthorizedError('Invalid or expired token', 'INVALID_TOKEN'));
        }
    }
};
exports.authenticate = authenticate;
//# sourceMappingURL=auth.middleware.js.map