"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncEntitySchema = exports.allowedEntities = void 0;
const zod_1 = require("zod");
exports.allowedEntities = [
    "Bank Transactions",
    "Bank Transfers",
    "Credit Notes",
    "Employees",
    "Expense Claims",
    "Invoices",
    "Journals",
    "Manual Journals",
    "Payments",
    "Tracking Categories",
    "Tax Rates",
    "Attachments",
    "Profit and Loss with Tracking",
    "Profit and Loss without Tracking",
    "(P&L, Balance Sheet, Trial Balance)",
    "Balance Sheet without Tracking",
    "Trial Balance",
];
exports.syncEntitySchema = zod_1.z.enum(exports.allowedEntities);
//# sourceMappingURL=syncEntity.validator.js.map