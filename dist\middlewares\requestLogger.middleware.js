"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const config_1 = require("../config/config");
const requestLogger = (req, res, next) => {
    const correlationId = req.headers['x-correlation-id'] ||
        `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    req.headers['x-correlation-id'] = correlationId;
    res.setHeader('X-Correlation-ID', correlationId);
    const startTime = Date.now();
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
        const responseTime = Date.now() - startTime;
        const logData = {
            correlationId,
            method: req.method,
            url: req.originalUrl,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            ip: req.ip || req.socket.remoteAddress,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id || req.user?.userId,
            contentLength: res.get('Content-Length') || '0',
        };
        if (res.statusCode >= 500) {
            logger_1.default.error('Request completed with server error', logData);
        }
        else if (res.statusCode >= 400) {
        }
        else if (config_1.config.IS_DEVELOPMENT || responseTime > 1000) {
            logger_1.default.info('Request completed', logData);
        }
        originalEnd.call(this, chunk, encoding);
        return this;
    };
    next();
};
exports.requestLogger = requestLogger;
//# sourceMappingURL=requestLogger.middleware.js.map