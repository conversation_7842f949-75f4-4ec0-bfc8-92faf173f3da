"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const sync_controller_1 = require("@controllers/sync.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/status', auth_middleware_1.authenticate, sync_controller_1.getSyncStatusController);
router.post('/trigger', auth_middleware_1.authenticate, sync_controller_1.triggerSyncController);
router.post('/trigger-all', auth_middleware_1.authenticate, sync_controller_1.triggerAllSyncController);
router.get('/history', auth_middleware_1.authenticate, sync_controller_1.getSyncHistoryController);
router.get('/entities', auth_middleware_1.authenticate, sync_controller_1.getSupportedEntitiesController);
exports.default = router;
//# sourceMappingURL=sync.routes.js.map