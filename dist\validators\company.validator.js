"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.companyIdSchema = exports.updateCompanySchema = exports.createCompanySchema = exports.companyListQuerySchema = void 0;
const zod_1 = require("zod");
exports.companyListQuerySchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, 'Name filter cannot be empty')
        .max(100, 'Name filter too long')
        .optional(),
    connectionStatus: zod_1.z
        .enum(['ACTIVE', 'EXPIRED', 'DISCONNECTED', 'PENDING'], {
        errorMap: () => ({
            message: 'Connection status must be ACTIVE, EXPIRED, DISCONNECTED, or PENDING',
        }),
    })
        .optional(),
    hasXeroConnection: zod_1.z
        .string()
        .optional()
        .transform((val) => {
        if (val === undefined)
            return undefined;
        if (val === 'true')
            return true;
        if (val === 'false')
            return false;
        throw new Error('hasXeroConnection must be "true" or "false"');
    }),
    createdAfter: zod_1.z
        .string()
        .optional()
        .transform((val) => {
        if (!val)
            return undefined;
        const date = new Date(val);
        if (isNaN(date.getTime())) {
            throw new Error('createdAfter must be a valid ISO date string');
        }
        return date;
    }),
    createdBefore: zod_1.z
        .string()
        .optional()
        .transform((val) => {
        if (!val)
            return undefined;
        const date = new Date(val);
        if (isNaN(date.getTime())) {
            throw new Error('createdBefore must be a valid ISO date string');
        }
        return date;
    }),
    limit: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? parseInt(val, 10) : 10))
        .refine((val) => !isNaN(val) && val >= 1 && val <= 100, {
        message: 'Limit must be a number between 1 and 100',
    }),
    offset: zod_1.z
        .string()
        .optional()
        .transform((val) => (val ? parseInt(val, 10) : 0))
        .refine((val) => !isNaN(val) && val >= 0, {
        message: 'Offset must be a non-negative number',
    }),
    sortBy: zod_1.z
        .enum(['name', 'createdAt', 'updatedAt'], {
        errorMap: () => ({ message: 'sortBy must be name, createdAt, or updatedAt' }),
    })
        .optional()
        .default('createdAt'),
    sortOrder: zod_1.z
        .enum(['asc', 'desc'], {
        errorMap: () => ({ message: 'sortOrder must be asc or desc' }),
    })
        .optional()
        .default('desc'),
});
exports.createCompanySchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, 'Company name is required')
        .max(150, 'Company name must be 150 characters or less')
        .trim(),
    financialYearEnd: zod_1.z
        .string()
        .max(20, 'Financial year end must be 20 characters or less')
        .optional(),
});
exports.updateCompanySchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, 'Company name cannot be empty')
        .max(150, 'Company name must be 150 characters or less')
        .trim()
        .optional(),
    financialYearEnd: zod_1.z
        .string()
        .max(20, 'Financial year end must be 20 characters or less')
        .optional(),
});
exports.companyIdSchema = zod_1.z.object({
    id: zod_1.z.string().uuid('Company ID must be a valid UUID'),
});
//# sourceMappingURL=company.validator.js.map