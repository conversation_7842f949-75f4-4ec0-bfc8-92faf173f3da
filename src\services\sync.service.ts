/**
 * @fileoverview
 * Entity Synchronization Service (refactored)
 * Handles Xero entity sync status, trigger, and history
 * with robust upsert SyncLog + ApiLog correlation
 */

import { prisma } from "@config/config";
import axiosClient from "@utils/axiosClient";
import logger from "@utils/logger";
import { v4 as uuidv4 } from "uuid";
import { BadRequestError, InternalServerError } from "@middlewares/error.middleware";
import { logApiCall } from "@utils/logApiCall";
import { lambdaEntityUrlMap } from "@config/sync.config";

/**
 * Supported Xero entities
 */
export const SYNC_ENTITIES = [
  "Accounts",
  "BankTransactions",
  "BankTransfers",
  "Budgets",
  "Contacts",
  "CreditNotes",
  "Currencies",
  "Employees",
  "ExpenseClaims",
  "Invoices",
  "Journals",
  "ManualJournals",
  "Items",
  "Payments",
  "PurchaseOrders",
  "TaxRates",
  "TrackingCategories",
  "Attachments",
  "TrialBalance",
  "ProfitLoss",
  "BalanceSheet",
] as const;

export type SyncEntity = (typeof SYNC_ENTITIES)[number];

/**
 * Interface for sync trigger request
 */
export interface SyncTriggerRequest {
  entities: SyncEntity[];
  companyId: string;
  priority?: "HIGH" | "NORMAL" | "LOW";
  fullSync?: boolean;
}

/**
 * Get synchronization status for all entities
 */
export const getSyncStatus = async (userId: string, companyId: string) => {
  try {
    const company = await prisma.company.findFirst({
      where: { Id: companyId, UserId: userId },
    });

    if (!company) {
      throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
    }

    const syncStatuses = await Promise.all(
      SYNC_ENTITIES.map(async (entity) => {
        const log = await prisma.syncLog.findFirst({
          where: { CompanyId: companyId, Entity: entity },
          orderBy: { StartedAt: "desc" },
        });

        let status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "NEVER_SYNCED" =
          "NEVER_SYNCED";

        if (log?.Status) {
          switch (log.Status) {
            case "PENDING":
            case "RETRYING":
              status = "PENDING";
              break;
            case "IN_PROGRESS":
              status = "IN_PROGRESS";
              break;
            case "SUCCESS":
            case "WARNING":
              status = "COMPLETED";
              break;
            case "ERROR":
            case "CANCELLED":
              status = "FAILED";
              break;
            default:
              status = "NEVER_SYNCED";
          }
        }

        return {
          entity,
          lastSync: log?.CompletedAt || log?.StartedAt || null,
          status,
          errorMessage: status === "FAILED" ? log?.Message || "" : "",
        };
      })
    );

    return {
      companyId,
      companyName: company.Name,
      connectionStatus: company.ConnectionStatus,
      entities: syncStatuses,
      lastUpdated: new Date(),
    };
  } catch (error) {
    logger.error("getSyncStatus failed", { userId, companyId, error });
    throw handleUnexpected(error, "SYNC_STATUS_RETRIEVAL_FAILED");
  }
};

/**
 * Trigger synchronization for specific entities
 * sending SQS messages (future) or direct Lambda calls (current)
 */
export const triggerEntitySync = async (
  userId: string,
  request: SyncTriggerRequest
) => {
  try {
    console.log("LAMBDA_SYNC_BANK_TRANSACTIONS_URL", request);
    const { entities, companyId, priority = "NORMAL", fullSync = false } = request;

    const company = await prisma.company.findFirst({
      where: { Id: companyId, UserId: userId },
    });

    if (!company) {
      throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
    }

    if (!company.XeroTenantId || company.ConnectionStatus !== "ACTIVE") {
      throw new BadRequestError(
        "Company does not have an active Xero connection",
        "NO_ACTIVE_XERO_CONNECTION"
      );
    }

    const triggerRecords: any[] = [];
    console.log("entities", entities);

    for (const entity of entities) {
      const requestId = uuidv4();
      const startedAt = new Date();
      const lambdaUrl = lambdaEntityUrlMap[entity];

      if (!lambdaUrl) {
        logger.warn(`No Lambda URL configured for entity: ${entity}`);
        continue;
      }

      try {
        const payload = { entity, companyId, fullSync, priority, requestId, dumpToDatabase: true };
        console.log(payload);
        console.log(lambdaUrl);

        // Call Lambda via centralized axios
        return await axiosClient.post(lambdaUrl, payload);

        // Upsert SyncLog

      } catch (error: any) {
        const completedAt = new Date();
        const duration = `${completedAt.getTime() - startedAt.getTime()}ms`;

        await prisma.syncLog.upsert({
          where: { Id: requestId },
          update: {
            Status: "ERROR",
            CompletedAt: completedAt,
            RetryCount: 1,
            Message: error.message || "Failed",
            ErrorDetails: error,
            Duration: duration,
            UpdatedAt: completedAt,
          },
          create: {
            Id: requestId,
            CompanyId: companyId,
            UserId: userId,
            Entity: entity,
            Integration: "Xero",
            Status: "ERROR",
            RequestId: requestId,
            StartedAt: startedAt,
            CompletedAt: completedAt,
            RequestPayload: { entity, companyId, fullSync, priority },
            ApiEndpoint: lambdaUrl,
            Method: "POST",
            Message: error.message || "Unknown error",
            ErrorDetails: error,
            Duration: duration,
            CreatedAt: startedAt,
          },
        });

        await logApiCall({
          companyId,
          userId,
          method: "POST",
          apiUrl: lambdaUrl,
          status: "500",
          integrationName: "Xero",
          apiName: `Sync ${entity}`,
          requestPayload: { entity, companyId, fullSync, priority },
          responsePayload: error.response?.data,
          duration,
        });

        triggerRecords.push({
          entity,
          status: "FAILED",
          requestId,
        });
      }
    }

    return {
      success: true,
      message: `Sync triggered for ${entities.length} entities`,
      data: {
        companyId,
        companyName: company.Name,
        triggerRecords,
        estimatedDuration: `${entities.length * 2}-${entities.length * 5} minutes`,
      },
    };
  }
  catch (error) {
    logger.error("triggerEntitySync failed", error);
    throw handleUnexpected(error, "ENTITY_SYNC_TRIGGER_FAILED");
  }
};

/**
 * Full sync for all entities
 */
export const triggerAllEntitiesSync = async (
  userId: string,
  companyId: string,
  options: { priority?: "HIGH" | "NORMAL" | "LOW"; fullSync?: boolean } = {}
) => {
  return triggerEntitySync(userId, {
    entities: [...SYNC_ENTITIES],
    companyId,
    ...(options.priority ? { priority: options.priority } : {}),
    ...(options.fullSync !== undefined ? { fullSync: options.fullSync } : {}),
  });
};

/**
 * Get sync history for a company
 */
export const getSyncHistory = async (
  userId: string,
  companyId: string,
  options: {
    limit?: number;
    offset?: number;
    entity?: SyncEntity;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
) => {
  const { limit = 50, offset = 0, entity, status, dateFrom, dateTo } = options;

  const company = await prisma.company.findFirst({
    where: { Id: companyId, UserId: userId },
  });

  if (!company) {
    throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
  }

  const where: any = { CompanyId: companyId };

  if (entity) {
    where.Entity = entity;
  }

  if (status) {
    const statusMap: Record<string, string> = {
      PENDING: "PENDING",
      IN_PROGRESS: "IN_PROGRESS",
      COMPLETED: "SUCCESS",
      FAILED: "ERROR",
    };
    where.Status = statusMap[status] || status;
  }

  if (dateFrom || dateTo) {
    where.StartedAt = {};
    if (dateFrom) where.StartedAt.gte = dateFrom;
    if (dateTo) where.StartedAt.lte = dateTo;
  }

  const [syncLogs, totalCount] = await Promise.all([
    prisma.syncLog.findMany({
      where,
      orderBy: { StartedAt: "desc" },
      take: limit,
      skip: offset,
    }),
    prisma.syncLog.count({ where }),
  ]);

  const formatted = syncLogs.map((log: any) => ({
    id: log.Id,
    entity: log.Entity,
    status: log.Status,
    errorMessage: log.Message,
    duration: log.Duration,
    createdAt: log.CreatedAt,
    completedAt: log.CompletedAt,
  }));

  return {
    companyId,
    companyName: company.Name,
    history: formatted,
    pagination: {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: Math.floor(offset / limit) + 1,
      limit,
      offset,
      hasNextPage: offset + limit < totalCount,
      hasPreviousPage: offset > 0,
    },
  };
};

/**
 * Utility to consistently handle unexpected errors
 */
const handleUnexpected = (error: unknown, code: string) => {
  if (error instanceof BadRequestError) return error;
  logger.error(`Unexpected error: ${error instanceof Error ? error.message : error}`);
  return new InternalServerError(
    error instanceof Error ? error.message : "Unknown error",
    code
  );
};
