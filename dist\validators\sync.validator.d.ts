import { z } from 'zod';
export declare const syncStatusQuerySchema: z.ZodObject<{
    companyId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    companyId: string;
}, {
    companyId: string;
}>;
export declare const syncTriggerSchema: z.ZodObject<{
    companyId: z.ZodString;
    entities: z.ZodEffects<z.ZodArray<z.ZodEnum<z.Writeable<any>>, "many">, any[], any[]>;
    priority: z.ZodDefault<z.ZodOptional<z.ZodEnum<["HIGH", "NORMAL", "LOW"]>>>;
    fullSync: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    companyId: string;
    entities: any[];
    priority: "HIGH" | "NORMAL" | "LOW";
    fullSync: boolean;
}, {
    companyId: string;
    entities: any[];
    priority?: "HIGH" | "NORMAL" | "LOW" | undefined;
    fullSync?: boolean | undefined;
}>;
export declare const syncHistoryQuerySchema: z.ZodEffects<z.ZodObject<{
    companyId: z.ZodString;
    limit: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, number, string | undefined>, number, string | undefined>;
    offset: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, number, string | undefined>, number, string | undefined>;
    entity: z.ZodOptional<z.ZodEnum<z.Writeable<any>>>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", "CANCELLED"]>>;
    dateFrom: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, Date | undefined, string | undefined>, Date | undefined, string | undefined>;
    dateTo: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, Date | undefined, string | undefined>, Date | undefined, string | undefined>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    offset: number;
    companyId: string;
    status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED" | undefined;
    entity?: any;
    dateFrom?: Date | undefined;
    dateTo?: Date | undefined;
}, {
    companyId: string;
    status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED" | undefined;
    limit?: string | undefined;
    offset?: string | undefined;
    entity?: any;
    dateFrom?: string | undefined;
    dateTo?: string | undefined;
}>, {
    limit: number;
    offset: number;
    companyId: string;
    status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED" | undefined;
    entity?: any;
    dateFrom?: Date | undefined;
    dateTo?: Date | undefined;
}, {
    companyId: string;
    status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED" | undefined;
    limit?: string | undefined;
    offset?: string | undefined;
    entity?: any;
    dateFrom?: string | undefined;
    dateTo?: string | undefined;
}>;
export declare const triggerAllSyncSchema: z.ZodObject<{
    companyId: z.ZodString;
    priority: z.ZodDefault<z.ZodOptional<z.ZodEnum<["HIGH", "NORMAL", "LOW"]>>>;
    fullSync: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    companyId: string;
    priority: "HIGH" | "NORMAL" | "LOW";
    fullSync: boolean;
}, {
    companyId: string;
    priority?: "HIGH" | "NORMAL" | "LOW" | undefined;
    fullSync?: boolean | undefined;
}>;
export declare const companyIdParamSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
export type SyncStatusQuery = z.infer<typeof syncStatusQuerySchema>;
export type SyncTriggerRequest = z.infer<typeof syncTriggerSchema>;
export type SyncHistoryQuery = z.infer<typeof syncHistoryQuerySchema>;
export type TriggerAllSyncRequest = z.infer<typeof triggerAllSyncSchema>;
export type CompanyIdParam = z.infer<typeof companyIdParamSchema>;
export declare const syncWebhookSchema: z.ZodObject<{
    syncId: z.ZodString;
    companyId: z.ZodString;
    entityType: z.ZodEnum<z.Writeable<any>>;
    status: z.ZodEnum<["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", "CANCELLED"]>;
    recordsProcessed: z.ZodOptional<z.ZodNumber>;
    recordsSucceeded: z.ZodOptional<z.ZodNumber>;
    recordsFailed: z.ZodOptional<z.ZodNumber>;
    errorMessage: z.ZodOptional<z.ZodString>;
    duration: z.ZodOptional<z.ZodNumber>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED";
    companyId: string;
    syncId: string;
    entityType?: any;
    recordsProcessed?: number | undefined;
    recordsSucceeded?: number | undefined;
    recordsFailed?: number | undefined;
    errorMessage?: string | undefined;
    duration?: number | undefined;
    metadata?: Record<string, any> | undefined;
}, {
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED";
    companyId: string;
    syncId: string;
    entityType?: any;
    recordsProcessed?: number | undefined;
    recordsSucceeded?: number | undefined;
    recordsFailed?: number | undefined;
    errorMessage?: string | undefined;
    duration?: number | undefined;
    metadata?: Record<string, any> | undefined;
}>;
export type SyncWebhookPayload = z.infer<typeof syncWebhookSchema>;
export declare const bulkSyncSchema: z.ZodObject<{
    companies: z.ZodArray<z.ZodObject<{
        companyId: z.ZodString;
        entities: z.ZodDefault<z.ZodOptional<z.ZodArray<z.ZodEnum<z.Writeable<any>>, "many">>>;
    }, "strip", z.ZodTypeAny, {
        companyId: string;
        entities: any[];
    }, {
        companyId: string;
        entities?: any[] | undefined;
    }>, "many">;
    priority: z.ZodDefault<z.ZodOptional<z.ZodEnum<["HIGH", "NORMAL", "LOW"]>>>;
    fullSync: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    companies: {
        companyId: string;
        entities: any[];
    }[];
    priority: "HIGH" | "NORMAL" | "LOW";
    fullSync: boolean;
}, {
    companies: {
        companyId: string;
        entities?: any[] | undefined;
    }[];
    priority?: "HIGH" | "NORMAL" | "LOW" | undefined;
    fullSync?: boolean | undefined;
}>;
export type BulkSyncRequest = z.infer<typeof bulkSyncSchema>;
//# sourceMappingURL=sync.validator.d.ts.map