"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUser = exports.findUserById = exports.logoutUser = exports.findUserByEmail = exports.createUser = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const createUser = async (data) => {
    logger_1.default.debug('Creating new user', {
        email: data.Email,
        hasName: !!data.Name,
        isActive: data.IsActive ?? true,
        isVerified: data.IsVerified ?? false,
    });
    try {
        const user = await config_1.prisma.user.create({
            data: {
                ...data,
                IsActive: data.IsActive ?? true,
                IsVerified: data.IsVerified ?? false,
            },
        });
        logger_1.default.info('User created successfully', {
            userId: user.Id,
            email: user.Email,
        });
        return user;
    }
    catch (error) {
        logger_1.default.error('Failed to create user', {
            email: data.Email,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.createUser = createUser;
const findUserByEmail = async (email) => {
    logger_1.default.debug('Finding user by email', { email });
    try {
        const user = await config_1.prisma.user.findUnique({
            where: { Email: email },
        });
        if (user) {
            logger_1.default.debug('User found by email', { userId: user.Id, email });
        }
        else {
            logger_1.default.debug('No user found with email', { email });
        }
        return user;
    }
    catch (error) {
        logger_1.default.error('Failed to find user by email', {
            email,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.findUserByEmail = findUserByEmail;
const logoutUser = async (userId) => {
    logger_1.default.debug('Logging out user', { userId });
    try {
        const user = await config_1.prisma.user.update({
            where: { Id: userId },
            data: {
                RefreshToken: null,
                TokenExpiry: null,
                LastLoginAt: new Date(),
            },
        });
        logger_1.default.info('User logged out successfully', { userId, email: user.Email });
        return user;
    }
    catch (error) {
        logger_1.default.error('Failed to logout user', {
            userId,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.logoutUser = logoutUser;
const findUserById = async (userId) => {
    logger_1.default.debug('Finding user by ID', { userId });
    try {
        const user = await config_1.prisma.user.findUnique({
            where: { Id: userId },
            select: {
                Id: true,
                Name: true,
                Email: true,
                IsActive: true,
                IsVerified: true,
                CreatedAt: true,
                UpdatedAt: true,
                LastLoginAt: true,
            },
        });
        if (user) {
            logger_1.default.debug('User found by ID', { userId, email: user.Email });
        }
        else {
            logger_1.default.debug('No user found with ID', { userId });
        }
        return user;
    }
    catch (error) {
        logger_1.default.error('Failed to find user by ID', {
            userId,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.findUserById = findUserById;
const updateUser = async (userId, data) => {
    logger_1.default.debug('Updating user', { userId, updateFields: Object.keys(data) });
    try {
        const user = await config_1.prisma.user.update({
            where: { Id: userId },
            data,
        });
        logger_1.default.info('User updated successfully', {
            userId,
            email: user.Email,
            updatedFields: Object.keys(data),
        });
        return user;
    }
    catch (error) {
        logger_1.default.error('Failed to update user', {
            userId,
            updateFields: Object.keys(data),
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.updateUser = updateUser;
//# sourceMappingURL=user.repository.js.map