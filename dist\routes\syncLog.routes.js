"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const syncLog_controller_1 = require("@controllers/syncLog.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/companies/:companyId/sync-logs', auth_middleware_1.authenticate, syncLog_controller_1.getSyncLogsController);
router.get('/sync-logs/:syncLogId', auth_middleware_1.authenticate, syncLog_controller_1.getSyncLogDetailsController);
router.post('/sync-logs/:syncLogId/retry', auth_middleware_1.authenticate, syncLog_controller_1.retrySyncController);
router.get('/companies/:companyId/sync-stats', auth_middleware_1.authenticate, syncLog_controller_1.getSyncStatsController);
exports.default = router;
//# sourceMappingURL=syncLog.routes.js.map