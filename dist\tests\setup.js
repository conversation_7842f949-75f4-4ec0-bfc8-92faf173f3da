"use strict";
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
process.env['DATABASE_URL'] = 'postgresql://test:test@localhost:5432/test_db';
jest.mock('../utils/logger', () => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
}));
jest.setTimeout(10000);
afterEach(() => {
    jest.clearAllMocks();
});
global.testConfig = {
    validUser: {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
    },
    invalidUser: {
        email: 'invalid-email',
        password: '123',
    },
    testJwtSecret: 'test-jwt-secret-key-for-testing-only',
};
//# sourceMappingURL=setup.js.map