{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/errorHandler.ts"], "names": [], "mappings": ";;;AAAA,6BAA+B;AAC/B,+CAAoE;AACpE,oEAOuC;AAWhC,MAAM,cAAc,GAAG,CAAC,KAAe,EAAmB,EAAE;IACjE,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACjD,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACzB,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC,CAAC,CAAC;IAEJ,OAAO,IAAI,kCAAe,CAAC,mBAAmB,EAAE;QAC9C,MAAM,EAAE,eAAe;QACvB,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM;KACjC,CAAC,CAAC;AACL,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB;AAMK,MAAM,iBAAiB,GAAG,CAAC,KAAU,EAAY,EAAE;IACxD,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;QACtE,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO,CAAC,CAAC,CAAC;gBAEb,MAAM,MAAM,GAAI,KAAK,CAAC,IAAI,EAAE,MAAmB,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC7D,OAAO,IAAI,gCAAa,CACtB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EACrC,6BAA6B,EAC7B,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,CAC/C,CAAC;YACJ,CAAC;YAED,KAAK,OAAO;gBACV,OAAO,IAAI,kCAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE;oBACjE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK;iBAC7B,CAAC,CAAC;YAEL,KAAK,OAAO;gBACV,OAAO,IAAI,kCAAe,CACxB,qCAAqC,EACrC,wBAAwB,EACxB,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,CAClC,CAAC;YAEJ,KAAK,OAAO;gBACV,OAAO,IAAI,kCAAe,CAAC,8BAA8B,EAAE,6BAA6B,EAAE;oBACxF,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa;iBACpC,CAAC,CAAC;YAEL;gBACE,OAAO,IAAI,sCAAmB,CAAC,2BAA2B,EAAE,gBAAgB,EAAE;oBAC5E,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAC;QACP,CAAC;IACH,CAAC;IAGD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,iCAAiC;YACpC,OAAO,IAAI,sCAAmB,CAAC,iCAAiC,EAAE,wBAAwB,CAAC,CAAC;QAE9F,KAAK,4BAA4B;YAC/B,OAAO,IAAI,sCAAmB,CAAC,uBAAuB,EAAE,uBAAuB,CAAC,CAAC;QAEnF,KAAK,iCAAiC;YACpC,OAAO,IAAI,sCAAmB,CAAC,4BAA4B,EAAE,2BAA2B,CAAC,CAAC;QAE5F,KAAK,6BAA6B;YAChC,OAAO,IAAI,kCAAe,CAAC,wBAAwB,EAAE,2BAA2B,CAAC,CAAC;QAEpF;YACE,OAAO,IAAI,sCAAmB,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;IACjF,CAAC;AACH,CAAC,CAAC;AAvDW,QAAA,iBAAiB,qBAuD5B;AAKK,MAAM,cAAc,GAAG,CAAC,KAAU,EAAqB,EAAE;IAC9D,IAAI,KAAK,YAAY,gCAAiB,EAAE,CAAC;QACvC,OAAO,IAAI,oCAAiB,CAAC,mBAAmB,EAAE,eAAe,EAAE;YACjE,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,IAAI,KAAK,YAAY,gCAAiB,EAAE,CAAC;QACvC,OAAO,IAAI,oCAAiB,CAAC,eAAe,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED,OAAO,IAAI,oCAAiB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;AACtE,CAAC,CAAC;AAZW,QAAA,cAAc,kBAYzB;AAMK,MAAM,WAAW,GAAG,CAAC,KAAU,EAAY,EAAE;IAElD,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QAC1D,OAAO,KAAiB,CAAC;IAC3B,CAAC;IAGD,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;QAC9B,OAAO,IAAA,sBAAc,EAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;QACrF,OAAO,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAGD,IAAI,KAAK,YAAY,gCAAiB,IAAI,KAAK,YAAY,gCAAiB,EAAE,CAAC;QAC7E,OAAO,IAAA,sBAAc,EAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;QAClC,OAAO,IAAI,sCAAmB,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC/B,OAAO,IAAI,sCAAmB,CAAC,4BAA4B,EAAE,wBAAwB,CAAC,CAAC;IACzF,CAAC;IAGD,OAAO,IAAI,sCAAmB,CAC5B,KAAK,CAAC,OAAO,IAAI,8BAA8B,EAC/C,eAAe,EACf,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CACjF,CAAC;AACJ,CAAC,CAAC;AApCW,QAAA,WAAW,eAoCtB;AAMK,MAAM,SAAS,GAAG,CAAqB,EAA8B,EAAE,EAAE;IAC9E,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;QACtC,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,SAAS,aAQpB"}