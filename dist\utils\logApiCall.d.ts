export interface LogApiCallInput {
    companyId: string;
    userId?: string;
    method: string;
    apiUrl: string;
    status: string;
    integrationName: string;
    apiName: string;
    requestPayload?: any;
    responsePayload?: any;
    duration?: string;
}
export declare const logApiCall: ({ companyId, userId, method, apiUrl, status, integrationName, apiName, requestPayload, responsePayload, duration, }: LogApiCallInput) => Promise<void>;
//# sourceMappingURL=logApiCall.d.ts.map