export interface CompanyListFilters {
    name?: string;
    connectionStatus?: 'ACTIVE' | 'EXPIRED' | 'DISCONNECTED' | 'PENDING';
    hasXeroConnection?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
    limit?: number;
    offset?: number;
    sortBy?: 'name' | 'createdAt' | 'updatedAt';
    sortOrder?: 'asc' | 'desc';
}
export declare const getXeroAuthUrl: () => string;
export declare const handleXeroCallback: (code: string, userId: string) => Promise<{
    Id: string;
    Name: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string;
    XeroTenantId: string | null;
    XeroAccessToken: string | null;
    XeroRefreshToken: string | null;
    XeroTokenExpiry: Date | null;
    XeroRefreshTokenExpiry: Date | null;
    FinancialYearEnd: string | null;
    LastSyncDate: Date | null;
    NextSyncDate: Date | null;
    ConnectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
}>;
export declare const disconnectXero: (companyId: string) => Promise<void>;
export declare const refreshXeroTokens: (companyId: string) => Promise<{
    Id: string;
    Name: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string;
    XeroTenantId: string | null;
    XeroAccessToken: string | null;
    XeroRefreshToken: string | null;
    XeroTokenExpiry: Date | null;
    XeroRefreshTokenExpiry: Date | null;
    FinancialYearEnd: string | null;
    LastSyncDate: Date | null;
    NextSyncDate: Date | null;
    ConnectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
}>;
export declare const getCompaniesWithFilters: (userId: string, filters?: CompanyListFilters) => Promise<{
    companies: {
        Id: string;
        Name: string;
        CreatedAt: Date;
        UpdatedAt: Date;
        XeroTenantId: string | null;
        XeroTokenExpiry: Date | null;
        FinancialYearEnd: string | null;
        ConnectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
    }[];
    pagination: {
        totalCount: number;
        totalPages: number;
        currentPage: number;
        limit: number;
        offset: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
    filters: CompanyListFilters;
}>;
export declare const getCompanyById: (userId: string, companyId: string) => Promise<{
    Id: string;
    Name: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    User: {
        Id: string;
        Name: string | null;
        Email: string;
    };
    XeroTenantId: string | null;
    XeroTokenExpiry: Date | null;
    XeroRefreshTokenExpiry: Date | null;
    FinancialYearEnd: string | null;
    ConnectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
} | null>;
export declare const disconnectCompanyXero: (userId: string, companyId: string) => Promise<{
    Id: string;
    Name: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    XeroTenantId: string | null;
    XeroTokenExpiry: Date | null;
    FinancialYearEnd: string | null;
    ConnectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
}>;
export declare const reconnectCompanyXero: (userId: string, companyId: string) => Promise<{
    authorizationUrl: string;
    companyId: string;
    companyName: string;
}>;
//# sourceMappingURL=xero.service.d.ts.map