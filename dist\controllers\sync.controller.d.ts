import { Request, Response, NextFunction } from 'express';
export declare const getSyncStatusController: (req: Request, res: Response, next: NextFunction) => void;
export declare const triggerSyncController: (req: Request, res: Response, next: NextFunction) => void;
export declare const triggerAllSyncController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSyncHistoryController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSupportedEntitiesController: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=sync.controller.d.ts.map