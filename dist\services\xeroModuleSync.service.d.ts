export declare const XERO_MODULES: readonly ["Accounts", "Bank Transactions", "Bank Transfers", "Budgets", "Contacts", "Credit Notes", "Currencies", "Employees", "Expense Claims", "Invoices", "Journals", "Manual Journals", "Payments", "Tracking Categories", "Tax Rates", "Attachments", "Reports (P&L, BS, TB)"];
export type XeroModuleName = typeof XERO_MODULES[number];
export interface ModuleSyncStatus {
    id: string;
    companyId: string;
    moduleName: string;
    lastSyncTime: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
export declare const createInitialModuleSyncRecords: (companyId: string) => Promise<ModuleSyncStatus[]>;
export declare const getModuleSyncStatus: (companyId: string) => Promise<ModuleSyncStatus[]>;
export declare const updateModuleSyncTime: (companyId: string, moduleName: XeroModuleName, syncTime?: Date) => Promise<ModuleSyncStatus>;
export declare const getModuleSyncStatusByName: (companyId: string, moduleName: XeroModuleName) => Promise<ModuleSyncStatus | null>;
export declare const areAllModulesSynced: (companyId: string) => Promise<boolean>;
//# sourceMappingURL=xeroModuleSync.service.d.ts.map