"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSyncSummaryController = exports.getAvailableModulesController = exports.updateModuleSyncTimeController = exports.getSpecificModuleSyncStatusController = exports.getModuleSyncStatusController = void 0;
const xeroModuleSync_service_1 = require("@services/xeroModuleSync.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const error_middleware_2 = require("@middlewares/error.middleware");
const getModuleSyncStatusHandler = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        if (!companyId) {
            throw new error_middleware_2.BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
        }
        const moduleSyncStatus = await (0, xeroModuleSync_service_1.getModuleSyncStatus)(companyId);
        const totalModules = xeroModuleSync_service_1.XERO_MODULES.length;
        const syncedModules = moduleSyncStatus.filter(module => module.lastSyncTime !== null).length;
        const pendingModules = totalModules - syncedModules;
        const allSynced = await (0, xeroModuleSync_service_1.areAllModulesSynced)(companyId);
        const responseData = {
            companyId,
            modules: moduleSyncStatus,
            summary: {
                totalModules,
                syncedModules,
                pendingModules,
                allSynced,
                syncProgress: totalModules > 0 ? Math.round((syncedModules / totalModules) * 100) : 0,
            },
        };
        res.status(200).json((0, response_1.successResponse)('Module sync status retrieved successfully', responseData));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getModuleSyncStatusController = (0, error_middleware_1.asyncErrorHandler)(getModuleSyncStatusHandler);
const getSpecificModuleSyncStatusHandler = async (req, res, next) => {
    try {
        const { companyId, moduleName } = req.params;
        if (!companyId) {
            throw new error_middleware_2.BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
        }
        if (!moduleName) {
            throw new error_middleware_2.BadRequestError('Module name is required', 'MISSING_MODULE_NAME');
        }
        if (!xeroModuleSync_service_1.XERO_MODULES.includes(moduleName)) {
            throw new error_middleware_2.BadRequestError(`Invalid module name. Valid modules are: ${xeroModuleSync_service_1.XERO_MODULES.join(', ')}`, 'INVALID_MODULE_NAME');
        }
        const moduleSyncStatus = await (0, xeroModuleSync_service_1.getModuleSyncStatusByName)(companyId, moduleName);
        if (!moduleSyncStatus) {
            throw new error_middleware_2.BadRequestError('Module sync status not found. Company may not be connected to Xero.', 'MODULE_SYNC_NOT_FOUND');
        }
        res.status(200).json((0, response_1.successResponse)('Module sync status retrieved successfully', moduleSyncStatus));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSpecificModuleSyncStatusController = (0, error_middleware_1.asyncErrorHandler)(getSpecificModuleSyncStatusHandler);
const updateModuleSyncTimeHandler = async (req, res, next) => {
    try {
        const { companyId, moduleName } = req.params;
        const { syncTime } = req.body;
        if (!companyId) {
            throw new error_middleware_2.BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
        }
        if (!moduleName) {
            throw new error_middleware_2.BadRequestError('Module name is required', 'MISSING_MODULE_NAME');
        }
        if (!xeroModuleSync_service_1.XERO_MODULES.includes(moduleName)) {
            throw new error_middleware_2.BadRequestError(`Invalid module name. Valid modules are: ${xeroModuleSync_service_1.XERO_MODULES.join(', ')}`, 'INVALID_MODULE_NAME');
        }
        let parsedSyncTime;
        if (syncTime) {
            parsedSyncTime = new Date(syncTime);
            if (isNaN(parsedSyncTime.getTime())) {
                throw new error_middleware_2.BadRequestError('Invalid sync time format', 'INVALID_SYNC_TIME');
            }
        }
        else {
            parsedSyncTime = new Date();
        }
        const updatedModuleSync = await (0, xeroModuleSync_service_1.updateModuleSyncTime)(companyId, moduleName, parsedSyncTime);
        res.status(200).json((0, response_1.successResponse)('Module sync time updated successfully', updatedModuleSync));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.updateModuleSyncTimeController = (0, error_middleware_1.asyncErrorHandler)(updateModuleSyncTimeHandler);
const getAvailableModulesHandler = async (_req, res, next) => {
    try {
        const responseData = {
            modules: xeroModuleSync_service_1.XERO_MODULES,
            totalCount: xeroModuleSync_service_1.XERO_MODULES.length,
        };
        res.status(200).json((0, response_1.successResponse)('Available Xero modules retrieved successfully', responseData));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getAvailableModulesController = (0, error_middleware_1.asyncErrorHandler)(getAvailableModulesHandler);
const getSyncSummaryHandler = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        if (!companyId) {
            throw new error_middleware_2.BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
        }
        const moduleSyncStatus = await (0, xeroModuleSync_service_1.getModuleSyncStatus)(companyId);
        const allSynced = await (0, xeroModuleSync_service_1.areAllModulesSynced)(companyId);
        const totalModules = xeroModuleSync_service_1.XERO_MODULES.length;
        const syncedModules = moduleSyncStatus.filter(module => module.lastSyncTime !== null).length;
        const pendingModules = totalModules - syncedModules;
        const lastSyncTimes = moduleSyncStatus
            .filter(module => module.lastSyncTime !== null)
            .map(module => module.lastSyncTime)
            .sort((a, b) => b.getTime() - a.getTime());
        const mostRecentSync = lastSyncTimes.length > 0 ? lastSyncTimes[0] : null;
        const oldestSync = lastSyncTimes.length > 0 ? lastSyncTimes[lastSyncTimes.length - 1] : null;
        const responseData = {
            companyId,
            summary: {
                totalModules,
                syncedModules,
                pendingModules,
                allSynced,
                syncProgress: totalModules > 0 ? Math.round((syncedModules / totalModules) * 100) : 0,
                mostRecentSync,
                oldestSync,
            },
            pendingModules: moduleSyncStatus
                .filter(module => module.lastSyncTime === null)
                .map(module => module.moduleName),
            syncedModules: moduleSyncStatus
                .filter(module => module.lastSyncTime !== null)
                .map(module => ({
                moduleName: module.moduleName,
                lastSyncTime: module.lastSyncTime,
            }))
                .sort((a, b) => b.lastSyncTime.getTime() - a.lastSyncTime.getTime()),
        };
        res.status(200).json((0, response_1.successResponse)('Sync summary retrieved successfully', responseData));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getSyncSummaryController = (0, error_middleware_1.asyncErrorHandler)(getSyncSummaryHandler);
//# sourceMappingURL=xeroModuleSync.controller.js.map