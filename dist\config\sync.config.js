"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.lambdaEntityUrlMap = void 0;
const config_1 = require("@config/config");
exports.lambdaEntityUrlMap = {
    "Bank Transactions": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BANK_TRANSACTIONS_URL,
    "Bank Transfers": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BANK_TRANSFERS_URL,
    "Credit Notes": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_CREDIT_NOTES_URL,
    "Employees": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_EMPLOYEES_URL,
    "Expense Claims": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_EXPENSE_CLAIMS_URL,
    "Invoices": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_INVOICES_URL,
    "Journals": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_JOURNALS_URL,
    "Manual Journals": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_MANUAL_JOURNALS_URL,
    "Payments": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_PAYMENTS_URL,
    "Tracking Categories": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TRACKING_CATEGORIES_URL,
    "Tax Rates": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TAX_RATES_URL,
    "Attachments": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_ATTACHMENTS_URL,
    "ProfitLoss": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_PL_TRACKING_URL,
    "Reports": config_1.config.LAMBDA_ENDPOINT.LAMBDA_BALANCE_SHEET,
    "BalanceSheet": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BS_NO_TRACKING_URL,
    "TrialBalance": config_1.config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TRIAL_BALANCE_URL,
};
//# sourceMappingURL=sync.config.js.map