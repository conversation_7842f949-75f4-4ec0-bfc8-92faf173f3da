export interface UserRegisterDTO {
    name?: string;
    email: string;
    password: string;
}
export interface UserLoginDTO {
    email: string;
    password: string;
}
export interface UserResponseDTO {
    id: string;
    name?: string;
    email: string;
    token?: string;
    expiresAt?: Date;
}
export interface UserCreateData {
    Name?: string;
    Email: string;
    Password: string;
    IsActive?: boolean;
    IsVerified?: boolean;
}
export interface UserUpdateData {
    Name?: string;
    Email?: string;
    Password?: string;
    IsActive?: boolean;
    IsVerified?: boolean;
    RefreshToken?: string | null;
    TokenExpiry?: Date | null;
    LastLoginAt?: Date;
}
export interface UserEntity {
    Id: string;
    Name: string | null;
    Email: string;
    Password: string;
    IsActive: boolean;
    IsVerified: boolean;
    RefreshToken: string | null;
    TokenExpiry: Date | null;
    CreatedAt: Date;
    UpdatedAt: Date;
    LastLoginAt: Date | null;
}
export interface SafeUserEntity {
    Id: string;
    Name: string | null;
    Email: string;
    IsActive: boolean;
    IsVerified: boolean;
    CreatedAt: Date;
    UpdatedAt: Date;
    LastLoginAt: Date | null;
}
//# sourceMappingURL=user.dto.d.ts.map