import { prisma } from '@config/config';
import logger from '@utils/logger';

/**
 * Xero modules that need to be tracked for sync status
 * These correspond to the different data entities available in Xero API
 */
export const XERO_MODULES = [
  'Accounts',
  'Bank Transactions',
  'Bank Transfers',
  'Budgets',
  'Contacts',
  'Credit Notes',
  'Currencies',
  'Employees',
  'Expense Claims',
  'Invoices',
  'Journals',
  'Manual Journals',
  'Payments',
  'Tracking Categories',
  'Tax Rates',
  'Attachments',
  'Reports (P&L, BS, TB)',
] as const;

export type XeroModuleName = typeof XERO_MODULES[number];

/**
 * Interface for module sync status
 */
export interface ModuleSyncStatus {
  id: string;
  companyId: string;
  moduleName: string;
  lastSyncTime: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Creates initial XeroModuleSync records for a company
 * This should be called when a company is first connected to Xero
 * 
 * @param companyId - The company ID to create module sync records for
 * @returns Promise<ModuleSyncStatus[]> - Array of created module sync records
 */
export const createInitialModuleSyncRecords = async (companyId: string): Promise<ModuleSyncStatus[]> => {
  try {
    logger.info('Creating initial module sync records', { companyId });

    // Check if records already exist for this company
    const existingRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: companyId },
    });

    if (existingRecords.length > 0) {
      logger.info('Module sync records already exist for company', {
        companyId,
        existingCount: existingRecords.length
      });
      return existingRecords.map(record => ({
        id: record.Id,
        companyId: record.CompanyId,
        moduleName: record.ModuleName,
        lastSyncTime: record.LastSyncTime,
        createdAt: record.CreatedAt,
        updatedAt: record.UpdatedAt,
      }));
    }

    // Create records for all modules
    const moduleRecords = XERO_MODULES.map(moduleName => ({
      CompanyId: companyId,
      ModuleName: moduleName,
      LastSyncTime: null, // Initially null - will be updated after first sync
    }));

    // Use createMany for efficient bulk insert
    await prisma.xeroModuleSync.createMany({
      data: moduleRecords,
      skipDuplicates: true, // Skip if any duplicates exist
    });

    // Fetch the created records to return
    const createdRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: companyId },
      orderBy: { ModuleName: 'asc' },
    });

    logger.info('Successfully created module sync records', {
      companyId,
      recordCount: createdRecords.length
    });

    return createdRecords.map(record => ({
      id: record.Id,
      companyId: record.CompanyId,
      moduleName: record.ModuleName,
      lastSyncTime: record.LastSyncTime,
      createdAt: record.CreatedAt,
      updatedAt: record.UpdatedAt,
    }));

  } catch (error: any) {
    logger.error('Error creating initial module sync records', {
      companyId,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to create module sync records: ${error.message}`);
  }
};

/**
 * Gets module sync status for a company
 * 
 * @param companyId - The company ID to get module sync status for
 * @returns Promise<ModuleSyncStatus[]> - Array of module sync status records
 */
export const getModuleSyncStatus = async (companyId: string): Promise<ModuleSyncStatus[]> => {
  try {
    logger.debug('Fetching module sync status', { companyId });

    const syncRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: companyId },
      orderBy: { ModuleName: 'asc' },
    });

    return syncRecords.map(record => ({
      id: record.Id,
      companyId: record.CompanyId,
      moduleName: record.ModuleName,
      lastSyncTime: record.LastSyncTime,
      createdAt: record.CreatedAt,
      updatedAt: record.UpdatedAt,
    }));

  } catch (error: any) {
    logger.error('Error fetching module sync status', {
      companyId,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to fetch module sync status: ${error.message}`);
  }
};

/**
 * Updates the last sync time for a specific module
 * This should be called after a successful sync operation
 * 
 * @param companyId - The company ID
 * @param moduleName - The module name to update
 * @param syncTime - The sync timestamp (defaults to current time)
 * @returns Promise<ModuleSyncStatus> - Updated module sync record
 */
export const updateModuleSyncTime = async (
  companyId: string,
  moduleName: XeroModuleName,
  syncTime: Date = new Date()
): Promise<ModuleSyncStatus> => {
  try {
    logger.debug('Updating module sync time', { companyId, moduleName, syncTime });

    const updatedRecord = await prisma.xeroModuleSync.update({
      where: {
        CompanyId_ModuleName: {
          CompanyId: companyId,
          ModuleName: moduleName,
        },
      },
      data: {
        LastSyncTime: syncTime,
      },
    });

    logger.info('Successfully updated module sync time', {
      companyId,
      moduleName,
      syncTime
    });

    return {
      id: updatedRecord.Id,
      companyId: updatedRecord.CompanyId,
      moduleName: updatedRecord.ModuleName,
      lastSyncTime: updatedRecord.LastSyncTime,
      createdAt: updatedRecord.CreatedAt,
      updatedAt: updatedRecord.UpdatedAt,
    };

  } catch (error: any) {
    logger.error('Error updating module sync time', {
      companyId,
      moduleName,
      syncTime,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to update module sync time: ${error.message}`);
  }
};

/**
 * Gets sync status for a specific module
 * 
 * @param companyId - The company ID
 * @param moduleName - The module name to get status for
 * @returns Promise<ModuleSyncStatus | null> - Module sync status or null if not found
 */
export const getModuleSyncStatusByName = async (
  companyId: string,
  moduleName: XeroModuleName
): Promise<ModuleSyncStatus | null> => {
  try {
    logger.debug('Fetching module sync status by name', { companyId, moduleName });

    const syncRecord = await prisma.xeroModuleSync.findUnique({
      where: {
        CompanyId_ModuleName: {
          CompanyId: companyId,
          ModuleName: moduleName,
        },
      },
    });

    if (!syncRecord) {
      return null;
    }

    return {
      id: syncRecord.Id,
      companyId: syncRecord.CompanyId,
      moduleName: syncRecord.ModuleName,
      lastSyncTime: syncRecord.LastSyncTime,
      createdAt: syncRecord.CreatedAt,
      updatedAt: syncRecord.UpdatedAt,
    };

  } catch (error: any) {
    logger.error('Error fetching module sync status by name', {
      companyId,
      moduleName,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to fetch module sync status: ${error.message}`);
  }
};

/**
 * Checks if all modules have been synced at least once
 * 
 * @param companyId - The company ID to check
 * @returns Promise<boolean> - True if all modules have been synced, false otherwise
 */
export const areAllModulesSynced = async (companyId: string): Promise<boolean> => {
  try {
    const syncRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: companyId },
    });

    // Check if all modules have a LastSyncTime (not null)
    const allSynced = syncRecords.every(record => record.LastSyncTime !== null);

    logger.debug('Checked if all modules are synced', {
      companyId,
      totalModules: syncRecords.length,
      allSynced
    });

    return allSynced;

  } catch (error: any) {
    logger.error('Error checking if all modules are synced', {
      companyId,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to check module sync status: ${error.message}`);
  }
};
