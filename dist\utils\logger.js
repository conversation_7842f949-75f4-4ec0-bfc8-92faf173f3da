"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const winston_1 = __importDefault(require("winston"));
const config_1 = require("@config/config");
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
}));
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf((info) => {
    return JSON.stringify({
        ...info,
        hostname: process.env['HOSTNAME'] || 'unknown',
        pid: process.pid,
        environment: config_1.config.NODE_ENV,
    });
}));
const transports = [];
transports.push(new winston_1.default.transports.Console({
    format: config_1.config.IS_DEVELOPMENT ? developmentFormat : productionFormat,
}));
if (config_1.config.IS_PRODUCTION) {
    if (config_1.config.LOGGING.ERROR_FILE) {
        transports.push(new winston_1.default.transports.File({
            filename: config_1.config.LOGGING.ERROR_FILE,
            level: 'error',
            format: productionFormat,
            maxsize: 5 * 1024 * 1024,
            maxFiles: 5,
        }));
    }
    if (config_1.config.LOGGING.COMBINED_FILE) {
        transports.push(new winston_1.default.transports.File({
            filename: config_1.config.LOGGING.COMBINED_FILE,
            format: productionFormat,
            maxsize: 5 * 1024 * 1024,
            maxFiles: 5,
        }));
    }
}
const logger = winston_1.default.createLogger({
    level: config_1.config.LOGGING.LEVEL,
    format: config_1.config.IS_PRODUCTION ? productionFormat : developmentFormat,
    defaultMeta: {
        service: 'furgal-backend',
        version: process.env['npm_package_version'] || '1.0.0',
    },
    transports,
    exceptionHandlers: [new winston_1.default.transports.File({ filename: 'logs/exceptions.log' })],
    rejectionHandlers: [new winston_1.default.transports.File({ filename: 'logs/rejections.log' })],
    exitOnError: false,
});
logger.stream = {
    write: (message) => {
    },
};
exports.default = logger;
//# sourceMappingURL=logger.js.map