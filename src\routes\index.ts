import { Router } from 'express';
import userRoutes from '@routes/user.routes';
import xeroRoutes from '@routes/xero.routes';
import companyRoutes from '@routes/company.routes';
import syncRoutes from '@routes/sync.routes';
import syncLogRoutes from '@routes/syncLog.routes';
import xeroModuleSyncRoutes from '@routes/xeroModuleSync.routes';

// create a new router instance
const router = Router();

router.use('/user', userRoutes);
router.use('/xero', xeroRoutes);
router.use('/companies', companyRoutes);
router.use('/sync', syncRoutes);
router.use('/syncLogs', syncLogRoutes); // Mount sync log routes at root level for flexibility
router.use('/', xeroModuleSyncRoutes); // Mount xero module sync routes at root level

export default router;
