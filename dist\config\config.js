"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWT_SECRET = exports.PORT = exports.prisma = exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    PORT: zod_1.z.string().transform(Number).default('8000'),
    HOST: zod_1.z.string().default('localhost'),
    DATABASE_URL: zod_1.z.string().min(1, 'DATABASE_URL is required'),
    JWT_SECRET: zod_1.z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
    JWT_EXPIRES_IN: zod_1.z.string().default('1h'),
    JWT_REFRESH_EXPIRES_IN: zod_1.z.string().default('7d'),
    CORS_ORIGIN: zod_1.z.string().default('http://localhost:8080'),
    CORS_CREDENTIALS: zod_1.z
        .string()
        .transform((val) => val === 'true')
        .default('true'),
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().transform(Number).default('900000'),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().transform(Number).default('100'),
    LOG_LEVEL: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    LOG_FILE_ERROR: zod_1.z.string().optional(),
    LOG_FILE_COMBINED: zod_1.z.string().optional(),
    BCRYPT_ROUNDS: zod_1.z.string().transform(Number).default('12'),
    SESSION_SECRET: zod_1.z.string().optional(),
    MAX_FILE_SIZE: zod_1.z.string().transform(Number).default('5242880'),
    UPLOAD_PATH: zod_1.z.string().default('uploads/'),
    API_VERSION: zod_1.z.string().default('v1'),
    API_PREFIX: zod_1.z.string().default('/api'),
    XERO_CLIENT_ID: zod_1.z.string().min(1, 'XERO_CLIENT_ID is required'),
    XERO_CLIENT_SECRET: zod_1.z.string().min(1, 'XERO_CLIENT_SECRET is required'),
    XERO_REDIRECT_URI: zod_1.z.string().url('XERO_REDIRECT_URI must be a valid URL'),
    XERO_AUTH_URL: zod_1.z.string().url('XERO_AUTH_URL must be a valid URL'),
    XERO_TOKEN_URL: zod_1.z.string().url('XERO_TOKEN_URL must be a valid URL'),
    XERO_REVOKE_URL: zod_1.z.string().url('XERO_API_BASE_URL must be a valid URL'),
});
const env = envSchema.parse(process.env);
exports.config = {
    NODE_ENV: env.NODE_ENV,
    PORT: env.PORT,
    HOST: env.HOST,
    IS_PRODUCTION: env.NODE_ENV === 'production',
    IS_DEVELOPMENT: env.NODE_ENV === 'development',
    IS_TEST: env.NODE_ENV === 'test',
    DATABASE_URL: env.DATABASE_URL,
    JWT: {
        SECRET: env.JWT_SECRET,
        EXPIRES_IN: env.JWT_EXPIRES_IN,
        REFRESH_EXPIRES_IN: env.JWT_REFRESH_EXPIRES_IN,
    },
    CORS: {
        ORIGIN: env.CORS_ORIGIN.split(',').map((origin) => origin.trim()),
        CREDENTIALS: env.CORS_CREDENTIALS,
    },
    RATE_LIMIT: {
        WINDOW_MS: env.RATE_LIMIT_WINDOW_MS,
        MAX_REQUESTS: env.RATE_LIMIT_MAX_REQUESTS,
    },
    LOGGING: {
        LEVEL: env.LOG_LEVEL,
        ERROR_FILE: env.LOG_FILE_ERROR,
        COMBINED_FILE: env.LOG_FILE_COMBINED,
    },
    SECURITY: {
        BCRYPT_ROUNDS: env.BCRYPT_ROUNDS,
        SESSION_SECRET: env.SESSION_SECRET,
    },
    UPLOAD: {
        MAX_FILE_SIZE: env.MAX_FILE_SIZE,
        PATH: env.UPLOAD_PATH,
    },
    API: {
        VERSION: env.API_VERSION,
        PREFIX: env.API_PREFIX,
    },
    XERO: {
        XERO_CLIENT_ID: env.XERO_CLIENT_ID,
        XERO_CLIENT_SECRET: env.XERO_CLIENT_SECRET,
        XERO_REDIRECT_URI: env.XERO_REDIRECT_URI,
        XERO_AUTH_URL: env.XERO_AUTH_URL,
        XERO_TOKEN_URL: env.XERO_TOKEN_URL,
        REVOKE_URL: env.XERO_REVOKE_URL,
    },
};
const prismaConfig = {
    log: exports.config.IS_DEVELOPMENT ? ['query', 'info', 'warn', 'error'] : ['error'],
    errorFormat: 'pretty',
};
exports.prisma = new client_1.PrismaClient(prismaConfig);
process.on('beforeExit', async () => {
    await exports.prisma.$disconnect();
});
exports.PORT = exports.config.PORT;
exports.JWT_SECRET = exports.config.JWT.SECRET;
//# sourceMappingURL=config.js.map