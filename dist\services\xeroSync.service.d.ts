export declare const syncXeroAccounts: (userId: string, companyId: string, fullSync?: boolean) => Promise<{
    success: boolean;
    recordsProcessed: number;
    message: string;
}>;
export declare const syncXeroInvoices: (userId: string, companyId: string, fullSync?: boolean) => Promise<{
    success: boolean;
    recordsProcessed: number;
    message: string;
}>;
export declare const syncXeroBankTransactions: (userId: string, companyId: string, fullSync?: boolean) => Promise<{
    success: boolean;
    recordsProcessed: number;
    message: string;
}>;
//# sourceMappingURL=xeroSync.service.d.ts.map