"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("@config/config");
if (!config_1.config.JWT.SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
}
const generateToken = (payload, expiresIn = '1h') => {
    const options = { expiresIn };
    return jsonwebtoken_1.default.sign(payload, config_1.config.JWT.SECRET, options);
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    const decoded = jsonwebtoken_1.default.verify(token, config_1.config.JWT.SECRET);
    if (typeof decoded === 'string') {
        throw new Error('Invalid token payload structure');
    }
    return decoded;
};
exports.verifyToken = verifyToken;
//# sourceMappingURL=token.js.map