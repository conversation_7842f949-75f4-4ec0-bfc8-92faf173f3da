"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const company_controller_1 = require("@controllers/company.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/', auth_middleware_1.authenticate, company_controller_1.getCompanies);
router.get('/stats', auth_middleware_1.authenticate, company_controller_1.getCompanyStats);
router.get('/:id', auth_middleware_1.authenticate, company_controller_1.getCompanyByIdController);
router.delete('/:id/disconnect', auth_middleware_1.authenticate, company_controller_1.disconnectCompanyXeroController);
router.post('/:id/reconnect', auth_middleware_1.authenticate, company_controller_1.reconnectCompanyXeroController);
exports.default = router;
//# sourceMappingURL=company.routes.js.map