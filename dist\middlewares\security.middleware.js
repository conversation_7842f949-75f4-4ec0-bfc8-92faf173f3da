"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityMiddleware = void 0;
const config_1 = require("../config/config");
const error_middleware_1 = require("./error.middleware");
const securityMiddleware = (req, res, next) => {
    res.removeHeader('X-Powered-By');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    if (config_1.config.IS_PRODUCTION) {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
        const contentType = req.get('Content-Type');
        if (contentType &&
            !contentType.includes('application/json') &&
            !contentType.includes('multipart/form-data')) {
            return next(new error_middleware_1.BadRequestError('Invalid Content-Type. Expected application/json or multipart/form-data', 'INVALID_CONTENT_TYPE'));
        }
    }
    const contentLength = parseInt(req.get('Content-Length') || '0');
    if (contentLength > config_1.config.UPLOAD.MAX_FILE_SIZE) {
        return next(new error_middleware_1.BadRequestError(`Request too large. Maximum size is ${config_1.config.UPLOAD.MAX_FILE_SIZE} bytes`, 'REQUEST_TOO_LARGE'));
    }
    next();
};
exports.securityMiddleware = securityMiddleware;
//# sourceMappingURL=security.middleware.js.map