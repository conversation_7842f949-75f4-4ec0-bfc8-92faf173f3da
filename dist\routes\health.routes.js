"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const config_1 = require("../config/config");
const response_1 = require("../utils/response");
const error_middleware_1 = require("../middlewares/error.middleware");
const logger_1 = __importDefault(require("../utils/logger"));
const router = (0, express_1.Router)();
router.get('/', (_req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env['NODE_ENV'] || 'development',
        version: process.env['npm_package_version'] || '1.0.0',
    });
});
router.get('/detailed', (0, error_middleware_1.asyncErrorHandler)(async (_req, res) => {
    const startTime = Date.now();
    let dbStatus = 'OK';
    let dbResponseTime = 0;
    try {
        const dbStart = Date.now();
        await config_1.prisma.$queryRaw `SELECT 1`;
        dbResponseTime = Date.now() - dbStart;
    }
    catch (error) {
        dbStatus = 'ERROR';
        logger_1.default.error('Database health check failed', { error: error.message });
    }
    const memoryUsage = process.memoryUsage();
    const healthData = {
        status: dbStatus === 'OK' ? 'OK' : 'DEGRADED',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env['NODE_ENV'] || 'development',
        version: process.env['npm_package_version'] || '1.0.0',
        responseTime: Date.now() - startTime,
        services: {
            database: {
                status: dbStatus,
                responseTime: dbResponseTime,
            },
        },
        system: {
            memory: {
                used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
                total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
                external: Math.round(memoryUsage.external / 1024 / 1024),
                rss: Math.round(memoryUsage.rss / 1024 / 1024),
            },
            cpu: {
                usage: process.cpuUsage(),
            },
        },
    };
    const statusCode = healthData.status === 'OK' ? 200 : 503;
    res.status(statusCode).json((0, response_1.successResponse)('Health check completed', healthData));
}));
router.get('/ready', (0, error_middleware_1.asyncErrorHandler)(async (_req, res) => {
    try {
        await config_1.prisma.$queryRaw `SELECT 1`;
        res.status(200).json({
            status: 'READY',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.default.error('Readiness check failed', { error: error.message });
        res.status(503).json({
            status: 'NOT_READY',
            timestamp: new Date().toISOString(),
            error: 'Database connection failed',
        });
    }
}));
router.get('/live', (_req, res) => {
    res.status(200).json({
        status: 'ALIVE',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
    });
});
exports.default = router;
//# sourceMappingURL=health.routes.js.map