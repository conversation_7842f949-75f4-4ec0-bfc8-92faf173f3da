"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paginatedResponse = exports.errorResponse = exports.successResponse = void 0;
const successResponse = (message, data) => {
    return {
        success: true,
        message,
        data,
        timestamp: new Date().toISOString(),
    };
};
exports.successResponse = successResponse;
const errorResponse = (error, details) => {
    return {
        success: false,
        error,
        data: details ?? {},
        timestamp: new Date().toISOString(),
    };
};
exports.errorResponse = errorResponse;
const paginatedResponse = (message, data, pagination) => {
    return {
        success: true,
        message,
        data,
        pagination,
        timestamp: new Date().toISOString(),
    };
};
exports.paginatedResponse = paginatedResponse;
//# sourceMappingURL=response.js.map