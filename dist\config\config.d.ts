import { PrismaClient, Prisma } from '@prisma/client';
export declare const config: {
    readonly NODE_ENV: "development" | "production" | "test";
    readonly PORT: number;
    readonly HOST: string;
    readonly IS_PRODUCTION: boolean;
    readonly IS_DEVELOPMENT: boolean;
    readonly IS_TEST: boolean;
    readonly DATABASE_URL: string;
    readonly JWT: {
        readonly SECRET: string;
        readonly EXPIRES_IN: string;
        readonly REFRESH_EXPIRES_IN: string;
    };
    readonly CORS: {
        readonly ORIGIN: string[];
        readonly CREDENTIALS: boolean;
    };
    readonly RATE_LIMIT: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: number;
    };
    readonly LOGGING: {
        readonly LEVEL: "error" | "warn" | "info" | "debug";
        readonly ERROR_FILE: string | undefined;
        readonly COMBINED_FILE: string | undefined;
    };
    readonly SECURITY: {
        readonly BCRYPT_ROUNDS: number;
        readonly SESSION_SECRET: string | undefined;
    };
    readonly UPLOAD: {
        readonly MAX_FILE_SIZE: number;
        readonly PATH: string;
    };
    readonly API: {
        readonly VERSION: string;
        readonly PREFIX: string;
    };
    readonly XERO: {
        readonly XERO_CLIENT_ID: string;
        readonly XERO_CLIENT_SECRET: string;
        readonly XERO_REDIRECT_URI: string;
        readonly XERO_AUTH_URL: string;
        readonly XERO_TOKEN_URL: string;
        readonly REVOKE_URL: string;
    };
};
export declare const prisma: PrismaClient<{
    datasources?: Prisma.Datasources;
    datasourceUrl?: string;
    errorFormat?: Prisma.ErrorFormat;
    log?: (Prisma.LogLevel | Prisma.LogDefinition)[];
    transactionOptions?: {
        maxWait?: number;
        timeout?: number;
        isolationLevel?: Prisma.TransactionIsolationLevel;
    };
    omit?: Prisma.GlobalOmitConfig;
}, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const PORT: number;
export declare const JWT_SECRET: string;
//# sourceMappingURL=config.d.ts.map