import { PrismaClient, Prisma } from '@prisma/client';
export declare const config: {
    readonly NODE_ENV: "development" | "production" | "test";
    readonly PORT: number;
    readonly HOST: string;
    readonly IS_PRODUCTION: boolean;
    readonly IS_DEVELOPMENT: boolean;
    readonly IS_TEST: boolean;
    readonly DATABASE_URL: string;
    readonly JWT: {
        readonly SECRET: string;
        readonly EXPIRES_IN: string;
        readonly REFRESH_EXPIRES_IN: string;
    };
    readonly CORS: {
        readonly ORIGIN: string[];
        readonly CREDENTIALS: boolean;
    };
    readonly RATE_LIMIT: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: number;
    };
    readonly LOGGING: {
        readonly LEVEL: "error" | "warn" | "info" | "debug";
        readonly ERROR_FILE: string | undefined;
        readonly COMBINED_FILE: string | undefined;
    };
    readonly SECURITY: {
        readonly BCRYPT_ROUNDS: number;
        readonly SESSION_SECRET: string | undefined;
    };
    readonly UPLOAD: {
        readonly MAX_FILE_SIZE: number;
        readonly PATH: string;
    };
    readonly API: {
        readonly VERSION: string;
        readonly PREFIX: string;
    };
    readonly XERO: {
        readonly XERO_CLIENT_ID: string;
        readonly XERO_CLIENT_SECRET: string;
        readonly XERO_REDIRECT_URI: string;
        readonly XERO_AUTH_URL: string;
        readonly XERO_TOKEN_URL: string;
        readonly REVOKE_URL: string;
    };
    readonly LAMBDA_ENDPOINT: {
        readonly LAMBDA_SYNC_BANK_TRANSACTIONS_URL: string;
        readonly LAMBDA_SYNC_BANK_TRANSFERS_URL: string;
        readonly LAMBDA_SYNC_CREDIT_NOTES_URL: string;
        readonly LAMBDA_SYNC_EMPLOYEES_URL: string;
        readonly LAMBDA_SYNC_EXPENSE_CLAIMS_URL: string;
        readonly LAMBDA_SYNC_INVOICES_URL: string;
        readonly LAMBDA_SYNC_JOURNALS_URL: string;
        readonly LAMBDA_SYNC_MANUAL_JOURNALS_URL: string;
        readonly LAMBDA_SYNC_PAYMENTS_URL: string;
        readonly LAMBDA_SYNC_TRACKING_CATEGORIES_URL: string;
        readonly LAMBDA_SYNC_TAX_RATES_URL: string;
        readonly LAMBDA_SYNC_ATTACHMENTS_URL: string;
        readonly LAMBDA_SYNC_PL_TRACKING_URL: string;
        readonly LAMBDA_BALANCE_SHEET: string;
        readonly LAMBDA_SYNC_BS_NO_TRACKING_URL: string;
        readonly LAMBDA_SYNC_TRIAL_BALANCE_URL: string;
    };
};
export declare const prisma: PrismaClient<{
    datasources?: Prisma.Datasources;
    datasourceUrl?: string;
    errorFormat?: Prisma.ErrorFormat;
    log?: (Prisma.LogLevel | Prisma.LogDefinition)[];
    transactionOptions?: {
        maxWait?: number;
        timeout?: number;
        isolationLevel?: Prisma.TransactionIsolationLevel;
    };
    omit?: Prisma.GlobalOmitConfig;
}, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const PORT: number;
export declare const JWT_SECRET: string;
//# sourceMappingURL=config.d.ts.map