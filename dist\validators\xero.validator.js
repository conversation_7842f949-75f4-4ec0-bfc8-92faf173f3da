"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.companyInfoSchema = exports.companyIdSchema = exports.xeroCallbackQuerySchema = void 0;
const zod_1 = require("zod");
const uuidSchema = zod_1.z
    .string()
    .min(1, 'ID cannot be empty')
    .uuid('Must be a valid UUID format')
    .transform((id) => id.trim());
const authCodeSchema = zod_1.z
    .string()
    .min(1, 'Authorization code is required')
    .max(2048, 'Authorization code is too long')
    .regex(/^[A-Za-z0-9_-]+$/, 'Authorization code contains invalid characters')
    .transform((code) => code.trim());
exports.xeroCallbackQuerySchema = zod_1.z.object({
    code: authCodeSchema,
    state: uuidSchema,
});
exports.companyIdSchema = zod_1.z.object({
    companyId: uuidSchema,
});
exports.companyInfoSchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, 'Company name is required')
        .max(150, 'Company name must not exceed 150 characters')
        .regex(/^[a-zA-Z0-9\s&.,'-]+$/, 'Company name contains invalid characters')
        .transform((name) => name.trim()),
    financialYearEnd: zod_1.z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, 'Financial year end must be in YYYY-MM-DD format')
        .optional(),
});
//# sourceMappingURL=xero.validator.js.map