export declare const successResponse: <T>(message: string, data: T) => {
    success: boolean;
    message: string;
    data: T;
    timestamp: string;
};
export declare const errorResponse: <DetailsType = Record<string, unknown>>(error: string, details?: DetailsType) => {
    success: boolean;
    error: string;
    data: {};
    timestamp: string;
};
export declare const paginatedResponse: <T>(message: string, data: T[], pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
}) => {
    success: boolean;
    message: string;
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
    timestamp: string;
};
//# sourceMappingURL=response.d.ts.map