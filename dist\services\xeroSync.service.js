"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncXeroBankTransactions = exports.syncXeroInvoices = exports.syncXeroAccounts = void 0;
const axios_1 = __importDefault(require("axios"));
const database_1 = require("@config/database");
const xero_service_1 = require("@services/xero.service");
const syncLogger_1 = require("@utils/syncLogger");
const error_middleware_1 = require("@middlewares/error.middleware");
const logger_1 = __importDefault(require("@utils/logger"));
const syncXeroAccounts = async (userId, companyId, fullSync = false) => {
    let context = null;
    try {
        const company = await database_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        if (!company.XeroTenantId || !company.XeroAccessToken) {
            throw new error_middleware_1.BadRequestError('Company does not have an active Xero connection', 'NO_XERO_CONNECTION');
        }
        context = await (0, syncLogger_1.startSyncOperation)({
            entity: 'Accounts',
            integration: 'Xero',
            apiEndpoint: 'https://api.xero.com/api.xro/2.0/Accounts',
            method: 'GET',
            companyId,
            userId,
            requestPayload: {
                fullSync,
                tenantId: company.XeroTenantId,
            },
        });
        let accessToken = company.XeroAccessToken;
        if (company.XeroTokenExpiry && new Date() >= company.XeroTokenExpiry) {
            logger_1.default.info('Xero token expired, refreshing...', { companyId });
            const refreshedCompany = await (0, xero_service_1.refreshXeroTokens)(companyId);
            accessToken = refreshedCompany.XeroAccessToken;
        }
        const apiUrl = 'https://api.xero.com/api.xro/2.0/Accounts';
        const headers = {
            'Authorization': `Bearer ${accessToken}`,
            'Xero-tenant-id': company.XeroTenantId,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        };
        let queryParams = '';
        if (!fullSync && company.LastSyncDate) {
            const modifiedSince = company.LastSyncDate.toISOString();
            queryParams = `?ModifiedSince=${encodeURIComponent(modifiedSince)}`;
        }
        logger_1.default.info('Making Xero API request', {
            syncLogId: context.syncLogId,
            url: `${apiUrl}${queryParams}`,
            fullSync,
        });
        const response = await axios_1.default.get(`${apiUrl}${queryParams}`, {
            headers,
            timeout: 30000,
        });
        const accounts = response.data.Accounts || [];
        logger_1.default.info('Xero API response received', {
            syncLogId: context.syncLogId,
            accountsCount: accounts.length,
            status: response.status,
        });
        let processedCount = 0;
        let errorCount = 0;
        for (const xeroAccount of accounts) {
            try {
                await database_1.prisma.account.upsert({
                    where: {
                        Id: xeroAccount.AccountID,
                    },
                    update: {
                        Code: xeroAccount.Code,
                        Name: xeroAccount.Name,
                        Type: xeroAccount.Type,
                        BankAccountType: xeroAccount.BankAccountType,
                        Description: xeroAccount.Description,
                        TaxType: xeroAccount.TaxType,
                        EnablePaymentsToAccount: xeroAccount.EnablePaymentsToAccount,
                        ShowInExpenseClaims: xeroAccount.ShowInExpenseClaims,
                        SystemAccount: xeroAccount.Class,
                        ReportingCode: xeroAccount.ReportingCode,
                        ReportingCodeName: xeroAccount.ReportingCodeName,
                        UpdateUtcDate: xeroAccount.UpdatedDateUTC ? new Date(xeroAccount.UpdatedDateUTC) : null,
                        CompanyId: companyId,
                    },
                    create: {
                        Id: xeroAccount.AccountID,
                        Code: xeroAccount.Code,
                        Name: xeroAccount.Name,
                        Type: xeroAccount.Type,
                        BankAccountType: xeroAccount.BankAccountType,
                        Description: xeroAccount.Description,
                        TaxType: xeroAccount.TaxType,
                        EnablePaymentsToAccount: xeroAccount.EnablePaymentsToAccount,
                        ShowInExpenseClaims: xeroAccount.ShowInExpenseClaims,
                        SystemAccount: xeroAccount.Class,
                        ReportingCode: xeroAccount.ReportingCode,
                        ReportingCodeName: xeroAccount.ReportingCodeName,
                        UpdateUtcDate: xeroAccount.UpdatedDateUTC ? new Date(xeroAccount.UpdatedDateUTC) : null,
                        CompanyId: companyId,
                    },
                });
                processedCount++;
            }
            catch (dbError) {
                errorCount++;
                logger_1.default.warn('Failed to save account to database', {
                    syncLogId: context.syncLogId,
                    accountId: xeroAccount.AccountID,
                    error: dbError instanceof Error ? dbError.message : 'Unknown error',
                });
            }
        }
        await (0, syncLogger_1.updateCompanySyncDates)(companyId, 'Accounts');
        const hasWarnings = errorCount > 0;
        const message = hasWarnings
            ? `Accounts sync completed with warnings: ${processedCount} processed, ${errorCount} failed`
            : `Accounts sync completed successfully: ${processedCount} accounts processed`;
        await (0, syncLogger_1.completeSyncOperation)(context, {
            success: true,
            data: {
                totalAccounts: accounts.length,
                processedCount,
                errorCount,
                fullSync,
            },
            recordsProcessed: processedCount,
            warning: hasWarnings ? `${errorCount} accounts failed to save` : undefined,
        });
        logger_1.default.info('Xero accounts sync completed', {
            syncLogId: context.syncLogId,
            companyId,
            totalAccounts: accounts.length,
            processedCount,
            errorCount,
            fullSync,
        });
        return {
            success: true,
            recordsProcessed: processedCount,
            message,
        };
    }
    catch (error) {
        const errorObj = error instanceof Error ? error : new Error('Unknown error occurred');
        if (context) {
            await (0, syncLogger_1.handleSyncFailure)(context, errorObj, true);
        }
        logger_1.default.error('Xero accounts sync failed', {
            syncLogId: context?.syncLogId,
            companyId,
            userId,
            error: errorObj.message,
            stack: errorObj.stack,
        });
        throw errorObj;
    }
};
exports.syncXeroAccounts = syncXeroAccounts;
const syncXeroInvoices = async (userId, companyId, fullSync = false) => {
    throw new Error('Xero invoices sync not yet implemented');
};
exports.syncXeroInvoices = syncXeroInvoices;
const syncXeroBankTransactions = async (userId, companyId, fullSync = false) => {
    throw new Error('Xero bank transactions sync not yet implemented');
};
exports.syncXeroBankTransactions = syncXeroBankTransactions;
//# sourceMappingURL=xeroSync.service.js.map