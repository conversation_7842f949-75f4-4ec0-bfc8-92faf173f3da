"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.retrySyncOperation = exports.getSyncLogById = exports.getSyncLogs = exports.scheduleRetry = exports.calculateRetryDelay = exports.updateSyncLog = exports.createSyncLog = void 0;
const config_1 = require("@config/config");
const client_1 = require("@prisma/client");
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
const DEFAULT_RETRY_CONFIG = {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
};
const createSyncLog = async (request) => {
    try {
        const { requestId, entity, integration, apiEndpoint, method, companyId, userId, requestPayload, maxRetries = DEFAULT_RETRY_CONFIG.maxRetries, } = request;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                ...(userId && { UserId: userId }),
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const syncLog = await config_1.prisma.syncLog.create({
            data: {
                RequestId: requestId || `${entity}-${Date.now()}`,
                Entity: entity,
                Integration: integration,
                ApiEndpoint: apiEndpoint ?? null,
                Method: method ?? null,
                Status: client_1.SyncStatus.PENDING,
                CompanyId: companyId,
                UserId: userId ?? null,
                RequestPayload: requestPayload,
                MaxRetries: maxRetries,
                RetryCount: 0,
            },
        });
        logger_1.default.info('Sync log created successfully', {
            syncLogId: syncLog.Id,
            entity,
            integration,
            companyId,
            userId,
        });
        return syncLog;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to create sync log', {
            request,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to create sync log: ${errorMessage}`, 'SYNC_LOG_CREATION_FAILED');
    }
};
exports.createSyncLog = createSyncLog;
const updateSyncLog = async (request) => {
    try {
        const { id, status, message, duration, responsePayload, errorDetails } = request;
        const updateData = {
            Status: status,
            Message: message,
            Duration: duration,
            ResponsePayload: responsePayload,
            ErrorDetails: errorDetails,
            UpdatedAt: new Date(),
        };
        if (['SUCCESS', 'ERROR', 'WARNING', 'CANCELLED'].includes(status)) {
            updateData.CompletedAt = new Date();
        }
        const syncLog = await config_1.prisma.syncLog.update({
            where: { Id: id },
            data: updateData,
        });
        logger_1.default.info('Sync log updated successfully', {
            syncLogId: id,
            status,
            duration,
        });
        return syncLog;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to update sync log', {
            request,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to update sync log: ${errorMessage}`, 'SYNC_LOG_UPDATE_FAILED');
    }
};
exports.updateSyncLog = updateSyncLog;
const calculateRetryDelay = (retryCount, config = DEFAULT_RETRY_CONFIG) => {
    const delay = config.baseDelayMs * Math.pow(config.backoffMultiplier, retryCount);
    return Math.min(delay, config.maxDelayMs);
};
exports.calculateRetryDelay = calculateRetryDelay;
const scheduleRetry = async (syncLogId, errorDetails) => {
    try {
        const syncLog = await config_1.prisma.syncLog.findUnique({
            where: { Id: syncLogId },
        });
        if (!syncLog) {
            throw new error_middleware_1.BadRequestError('Sync log not found', 'SYNC_LOG_NOT_FOUND');
        }
        if (syncLog.RetryCount >= syncLog.MaxRetries) {
            return (0, exports.updateSyncLog)({
                id: syncLogId,
                status: client_1.SyncStatus.ERROR,
                message: `Max retries (${syncLog.MaxRetries}) exceeded`,
                errorDetails,
            });
        }
        const retryDelay = (0, exports.calculateRetryDelay)(syncLog.RetryCount);
        const nextRetryAt = new Date(Date.now() + retryDelay);
        const updatedSyncLog = await config_1.prisma.syncLog.update({
            where: { Id: syncLogId },
            data: {
                Status: client_1.SyncStatus.RETRYING,
                RetryCount: syncLog.RetryCount + 1,
                LastRetryAt: new Date(),
                NextRetryAt: nextRetryAt,
                ErrorDetails: errorDetails,
                Message: `Retry ${syncLog.RetryCount + 1}/${syncLog.MaxRetries} scheduled`,
            },
        });
        logger_1.default.info('Retry scheduled for sync log', {
            syncLogId,
            retryCount: updatedSyncLog.RetryCount,
            nextRetryAt,
            retryDelay,
        });
        return updatedSyncLog;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to schedule retry', {
            syncLogId,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to schedule retry: ${errorMessage}`, 'RETRY_SCHEDULE_FAILED');
    }
};
exports.scheduleRetry = scheduleRetry;
const getSyncLogs = async (userId, companyId, filters = {}) => {
    try {
        const { startDate, endDate, entity, status, integration, limit = 50, offset = 0 } = filters;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const whereClause = {
            CompanyId: companyId,
        };
        if (startDate) {
            whereClause.StartedAt = { gte: startDate };
        }
        if (endDate) {
            whereClause.StartedAt = {
                ...whereClause.StartedAt,
                lte: endDate,
            };
        }
        if (entity) {
            whereClause.Entity = entity;
        }
        if (status) {
            whereClause.Status = status;
        }
        if (integration) {
            whereClause.Integration = integration;
        }
        const [syncLogs, totalCount] = await Promise.all([
            config_1.prisma.syncLog.findMany({
                where: whereClause,
                orderBy: { StartedAt: 'desc' },
                take: Math.min(limit, 100),
                skip: offset,
                include: {
                    User: {
                        select: {
                            Id: true,
                            Name: true,
                            Email: true,
                        },
                    },
                },
            }),
            config_1.prisma.syncLog.count({
                where: whereClause,
            }),
        ]);
        logger_1.default.info('Sync logs retrieved successfully', {
            userId,
            companyId,
            totalCount,
            returnedCount: syncLogs.length,
        });
        return {
            syncLogs,
            pagination: {
                totalCount,
                limit,
                offset,
                hasMore: offset + syncLogs.length < totalCount,
            },
            filters: {
                startDate,
                endDate,
                entity,
                status,
                integration,
            },
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve sync logs', {
            userId,
            companyId,
            filters,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve sync logs: ${errorMessage}`, 'SYNC_LOGS_RETRIEVAL_FAILED');
    }
};
exports.getSyncLogs = getSyncLogs;
const getSyncLogById = async (userId, syncLogId) => {
    try {
        const syncLog = await config_1.prisma.syncLog.findFirst({
            where: {
                Id: syncLogId,
                Company: {
                    UserId: userId,
                },
            },
            include: {
                Company: {
                    select: {
                        Id: true,
                        Name: true,
                    },
                },
                User: {
                    select: {
                        Id: true,
                        Name: true,
                        Email: true,
                    },
                },
            },
        });
        if (!syncLog) {
            throw new error_middleware_1.BadRequestError('Sync log not found or access denied', 'SYNC_LOG_NOT_FOUND');
        }
        logger_1.default.info('Sync log details retrieved successfully', {
            userId,
            syncLogId,
        });
        return syncLog;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve sync log details', {
            userId,
            syncLogId,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve sync log: ${errorMessage}`, 'SYNC_LOG_RETRIEVAL_FAILED');
    }
};
exports.getSyncLogById = getSyncLogById;
const retrySyncOperation = async (userId, syncLogId) => {
    try {
        const syncLog = await config_1.prisma.syncLog.findFirst({
            where: {
                Id: syncLogId,
                Company: {
                    UserId: userId,
                },
            },
        });
        if (!syncLog) {
            throw new error_middleware_1.BadRequestError('Sync log not found or access denied', 'SYNC_LOG_NOT_FOUND');
        }
        if (syncLog.Status === client_1.SyncStatus.IN_PROGRESS) {
            throw new error_middleware_1.BadRequestError('Sync operation is already in progress', 'SYNC_IN_PROGRESS');
        }
        if (syncLog.RetryCount >= syncLog.MaxRetries) {
            throw new error_middleware_1.BadRequestError('Maximum retry attempts exceeded', 'MAX_RETRIES_EXCEEDED');
        }
        const updatedSyncLog = await config_1.prisma.syncLog.update({
            where: { Id: syncLogId },
            data: {
                Status: client_1.SyncStatus.PENDING,
                RetryCount: syncLog.RetryCount + 1,
                LastRetryAt: new Date(),
                NextRetryAt: null,
                Message: `Manual retry ${syncLog.RetryCount + 1}/${syncLog.MaxRetries} initiated`,
            },
        });
        logger_1.default.info('Manual retry initiated for sync log', {
            userId,
            syncLogId,
            retryCount: updatedSyncLog.RetryCount,
        });
        return {
            success: true,
            message: 'Retry initiated successfully',
            data: {
                syncLogId,
                retryCount: updatedSyncLog.RetryCount,
                maxRetries: updatedSyncLog.MaxRetries,
                status: updatedSyncLog.Status,
            },
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retry sync operation', {
            userId,
            syncLogId,
            error: errorMessage,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retry sync operation: ${errorMessage}`, 'SYNC_RETRY_FAILED');
    }
};
exports.retrySyncOperation = retrySyncOperation;
//# sourceMappingURL=syncLog.service.js.map