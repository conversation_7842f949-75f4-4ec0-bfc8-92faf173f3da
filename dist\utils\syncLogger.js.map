{"version": 3, "file": "syncLogger.js", "sourceRoot": "", "sources": ["../../src/utils/syncLogger.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,2CAA4C;AAC5C,+DAKmC;AACnC,2DAAmC;AA8B5B,MAAM,kBAAkB,GAAG,KAAK,EAAE,OAA6B,EAAwB,EAAE;IAC9F,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,IAAA,+BAAa,EAAC,OAAO,CAAC,CAAC;QAE7C,MAAM,OAAO,GAAgB;YAC3B,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC;QAGF,MAAM,IAAA,+BAAa,EAAC;YAClB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE,mBAAU,CAAC,WAAW;YAC9B,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,eAAe;SAC1C,CAAC,CAAC;QAEH,gBAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACpC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,kBAAkB,sBAmC7B;AAQK,MAAM,qBAAqB,GAAG,KAAK,EACxC,OAAoB,EACpB,MAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;QAChD,MAAM,WAAW,GAAG,GAAG,QAAQ,IAAI,CAAC;QAEpC,IAAI,MAAkB,CAAC;QACvB,IAAI,OAAe,CAAC;QAEpB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAU,CAAC,OAAO,CAAC;YAClE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,GAAG,OAAO,CAAC,MAAM,8BAA8B,CAAC;YAC5E,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,OAAO,IAAI,KAAK,MAAM,CAAC,gBAAgB,qBAAqB,CAAC;YAC/D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,mBAAU,CAAC,KAAK,CAAC;YAC1B,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI,GAAG,OAAO,CAAC,MAAM,cAAc,CAAC;QACrE,CAAC;QAED,MAAM,IAAA,+BAAa,EAAC;YAClB,EAAE,EAAE,OAAO,CAAC,SAAS;YACrB,MAAM;YACN,OAAO;YACP,QAAQ,EAAE,WAAW;YACrB,eAAe,EAAE,MAAM,CAAC,IAAI;YAC5B,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC3B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;gBACvB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;gBAC7B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK;aAC1B,CAAC,CAAC,CAAC,SAAS;SACd,CAAC,CAAC;QAEH,gBAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM;YACN,QAAQ,EAAE,WAAW;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAjDW,QAAA,qBAAqB,yBAiDhC;AASK,MAAM,iBAAiB,GAAG,KAAK,EACpC,OAAoB,EACpB,KAAY,EACZ,cAAuB,IAAI,EACW,EAAE;IACxC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;QAChD,MAAM,WAAW,GAAG,GAAG,QAAQ,IAAI,CAAC;QAEpC,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,WAAW,EAAE,CAAC;YAEhB,MAAM,IAAA,+BAAa,EAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAErD,gBAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;gBACpD,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,WAAW;aACtB,CAAC,CAAC;YAEH,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YAEN,MAAM,IAAA,+BAAa,EAAC;gBAClB,EAAE,EAAE,OAAO,CAAC,SAAS;gBACrB,MAAM,EAAE,mBAAU,CAAC,KAAK;gBACxB,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,iBAAiB,KAAK,CAAC,OAAO,EAAE;gBAC1D,QAAQ,EAAE,WAAW;gBACrB,YAAY;aACb,CAAC,CAAC;YAEH,gBAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBACxD,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,WAAW;aACtB,CAAC,CAAC;YAEH,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAAC,OAAO,QAAQ,EAAE,CAAC;QAClB,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC5C,OAAO;YACP,aAAa,EAAE,KAAK,CAAC,OAAO;YAC5B,QAAQ,EAAE,QAAQ,YAAY,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SACzE,CAAC,CAAC;QACH,MAAM,QAAQ,CAAC;IACjB,CAAC;AACH,CAAC,CAAC;AAvDW,QAAA,iBAAiB,qBAuD5B;AAQK,MAAM,eAAe,GAAG,KAAK,EAClC,OAA6B,EAC7B,SAA+C,EACnC,EAAE;IACd,IAAI,OAAO,GAAuB,IAAI,CAAC;IAEvC,IAAI,CAAC;QAEH,OAAO,GAAG,MAAM,IAAA,0BAAkB,EAAC,OAAO,CAAC,CAAC;QAG5C,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;QAGxC,MAAM,IAAA,6BAAqB,EAAC,OAAO,EAAE;YACnC,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,OAAO,EAAE,CAAC;YAEZ,MAAM,IAAA,yBAAiB,EAAC,OAAO,EAAE,KAAc,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA3BW,QAAA,eAAe,mBA2B1B;AAQK,MAAM,sBAAsB,GAAG,KAAK,EACzC,SAAiB,EACjB,MAAc,EACC,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,wDAAa,gBAAgB,GAAC,CAAC;QAElD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,IAAI,EAAE;gBACJ,YAAY,EAAE,GAAG;gBAEjB,YAAY,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aAC5D;SACF,CAAC,CAAC;QAEH,gBAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACxC,SAAS;YACT,MAAM;YACN,YAAY,EAAE,GAAG;SAClB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;YAClD,SAAS;YACT,MAAM;YACN,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC,CAAC;IAEL,CAAC;AACH,CAAC,CAAC;AA/BW,QAAA,sBAAsB,0BA+BjC"}