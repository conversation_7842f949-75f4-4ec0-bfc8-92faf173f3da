"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.logout = exports.login = exports.register = void 0;
const user_service_1 = require("@services/user.service");
const user_validator_1 = require("@validators/user.validator");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const registerHandler = async (req, res, next) => {
    try {
        const validated = user_validator_1.registerSchema.parse(req.body);
        const user = await (0, user_service_1.registerUser)(validated);
        const response = {
            id: user.id,
            email: user.email,
            name: user.name,
        };
        res.status(201).json((0, response_1.successResponse)('User registered successfully', response));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.register = (0, error_middleware_1.asyncError<PERSON>andler)(registerHandler);
const loginHandler = async (req, res, next) => {
    try {
        const validated = user_validator_1.loginSchema.parse(req.body);
        const { user, token, tokenExpiry } = await (0, user_service_1.loginUser)(validated);
        const response = {
            id: user.Id,
            email: user.Email,
            name: user.Name || '',
            token,
            expiresAt: tokenExpiry,
        };
        res.status(200).json((0, response_1.successResponse)('Login successful', response));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.login = (0, error_middleware_1.asyncErrorHandler)(loginHandler);
const logoutHandler = async (req, res, _next) => {
    await (0, user_service_1.logOutUser)(req.user);
    res.status(200).json((0, response_1.successResponse)('Logout successful', {
        message: 'Please remove your token on the client side.',
    }));
};
exports.logout = (0, error_middleware_1.asyncErrorHandler)(logoutHandler);
const getProfileHandler = async (req, res, next) => {
    try {
        const userId = req.user?.userId;
        const user = await (0, user_service_1.getUserProfile)(userId);
        if (!user) {
            throw new error_middleware_1.NotFoundError('User not found', 'USER_NOT_FOUND');
        }
        res.status(200).json((0, response_1.successResponse)('User profile retrieved successfully', user));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getProfile = (0, error_middleware_1.asyncErrorHandler)(getProfileHandler);
//# sourceMappingURL=user.controller.js.map