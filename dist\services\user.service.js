"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserProfile = exports.logOutUser = exports.loginUser = exports.registerUser = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const user_repository_1 = require("@repositories/user.repository");
const token_1 = require("@utils/token");
const error_middleware_1 = require("@middlewares/error.middleware");
const errorHandler_1 = require("@utils/errorHandler");
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const registerUserHandler = async (input) => {
    logger_1.default.info('Starting user registration process', { email: input.email });
    const existingUser = await (0, user_repository_1.findUserByEmail)(input.email);
    if (existingUser) {
        logger_1.default.warn('Registration attempt with existing email', { email: input.email });
        throw new error_middleware_1.ConflictError('A user already exists with this email address.', 'USER_ALREADY_EXISTS', { email: input.email });
    }
    logger_1.default.debug('Hashing password for new user', { email: input.email });
    const hashedPassword = await bcryptjs_1.default.hash(input.password, config_1.config.SECURITY.BCRYPT_ROUNDS);
    const userData = {
        Email: input.email,
        Password: hashedPassword,
        ...(input.name && { Name: input.name }),
        IsActive: true,
        IsVerified: false,
    };
    const user = await (0, user_repository_1.createUser)(userData);
    logger_1.default.info('User registration completed successfully', {
        userId: user.Id,
        email: user.Email,
    });
    return user;
};
exports.registerUser = (0, errorHandler_1.safeAsync)(registerUserHandler);
const loginUserHandler = async (input) => {
    logger_1.default.info('Starting user login process', { email: input.email });
    const user = await (0, user_repository_1.findUserByEmail)(input.email);
    if (!user) {
        logger_1.default.warn('Login attempt with non-existent email', { email: input.email });
        throw new error_middleware_1.UnauthorizedError('Invalid email or password.', 'INVALID_CREDENTIALS', {
            email: input.email,
        });
    }
    if (!user.IsActive) {
        logger_1.default.warn('Login attempt with inactive account', {
            userId: user.Id,
            email: user.Email,
        });
        throw new error_middleware_1.UnauthorizedError('Account is deactivated. Please contact support.', 'ACCOUNT_DEACTIVATED', { userId: user.Id });
    }
    const isPasswordValid = await bcryptjs_1.default.compare(input.password, user.Password);
    if (!isPasswordValid) {
        logger_1.default.warn('Login attempt with invalid password', {
            userId: user.Id,
            email: user.Email,
        });
        throw new error_middleware_1.UnauthorizedError('Invalid email or password.', 'INVALID_CREDENTIALS', {
            email: input.email,
        });
    }
    const tokenExpiryMs = convertExpiryToMills(config_1.config.JWT.EXPIRES_IN);
    const token = (0, token_1.generateToken)({
        userId: user.Id,
        email: user.Email,
        isVerified: user.IsVerified,
    }, tokenExpiryMs);
    const tokenExpiry = new Date(Date.now() + tokenExpiryMs);
    await (0, user_repository_1.updateUser)(user.Id, {
        LastLoginAt: new Date(),
    });
    logger_1.default.info('User login completed successfully', {
        userId: user.Id,
        email: user.Email,
    });
    return { user, token, tokenExpiry };
};
exports.loginUser = (0, errorHandler_1.safeAsync)(loginUserHandler);
const convertExpiryToMills = (expiresIn) => {
    if (typeof expiresIn === 'number')
        return expiresIn * 1000;
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match)
        return 3600000;
    const value = parseInt(match[1], 10);
    const unit = match[2];
    switch (unit) {
        case 's':
            return value * 1000;
        case 'm':
            return value * 60 * 1000;
        case 'h':
            return value * 60 * 60 * 1000;
        case 'd':
            return value * 24 * 60 * 60 * 1000;
        default:
            return 3600000;
    }
};
const logOutUserHandler = async (input) => {
    await (0, user_repository_1.logoutUser)(input.userId);
    return {
        message: 'User logged out successfully',
    };
};
exports.logOutUser = (0, errorHandler_1.safeAsync)(logOutUserHandler);
const getUserProfileHandler = async (userId) => {
    return (0, user_repository_1.findUserById)(userId);
};
exports.getUserProfile = (0, errorHandler_1.safeAsync)(getUserProfileHandler);
//# sourceMappingURL=user.service.js.map