import { SyncStatus } from '@prisma/client';
export interface CreateSyncLogRequest {
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    maxRetries?: number;
}
export interface UpdateSyncLogRequest {
    id: string;
    status: SyncStatus;
    message?: string;
    duration?: string;
    responsePayload?: any;
    errorDetails?: any;
}
export interface SyncLogFilters {
    startDate?: Date | undefined;
    endDate?: Date | undefined;
    entity?: string | undefined;
    status?: SyncStatus | undefined;
    integration?: string | undefined;
    limit?: number | undefined;
    offset?: number | undefined;
}
export interface RetryConfig {
    maxRetries: number;
    baseDelayMs: number;
    maxDelayMs: number;
    backoffMultiplier: number;
}
export declare const createSyncLog: (request: CreateSyncLogRequest) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string | null;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    ApiEndpoint: string | null;
    Method: string | null;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Message: string | null;
    Duration: string | null;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    StartedAt: Date;
    CompletedAt: Date | null;
    CompanyId: string;
    RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
    ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
}>;
export declare const updateSyncLog: (request: UpdateSyncLogRequest) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string | null;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    ApiEndpoint: string | null;
    Method: string | null;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Message: string | null;
    Duration: string | null;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    StartedAt: Date;
    CompletedAt: Date | null;
    CompanyId: string;
    RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
    ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
}>;
export declare const calculateRetryDelay: (retryCount: number, config?: RetryConfig) => number;
export declare const scheduleRetry: (syncLogId: string, errorDetails?: any) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string | null;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    ApiEndpoint: string | null;
    Method: string | null;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Message: string | null;
    Duration: string | null;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    StartedAt: Date;
    CompletedAt: Date | null;
    CompanyId: string;
    RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
    ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
}>;
export declare const getSyncLogs: (userId: string, companyId: string, filters?: SyncLogFilters) => Promise<{
    syncLogs: ({
        User: {
            Id: string;
            Name: string | null;
            Email: string;
        } | null;
    } & {
        Id: string;
        CreatedAt: Date;
        UpdatedAt: Date;
        UserId: string | null;
        RequestId: string | null;
        Entity: string;
        Integration: string;
        ApiEndpoint: string | null;
        Method: string | null;
        Status: import(".prisma/client").$Enums.SyncStatus;
        Message: string | null;
        Duration: string | null;
        RetryCount: number;
        MaxRetries: number;
        LastRetryAt: Date | null;
        NextRetryAt: Date | null;
        StartedAt: Date;
        CompletedAt: Date | null;
        CompanyId: string;
        RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
        ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
        ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
    })[];
    pagination: {
        totalCount: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
    filters: {
        startDate: Date | undefined;
        endDate: Date | undefined;
        entity: string | undefined;
        status: import(".prisma/client").$Enums.SyncStatus | undefined;
        integration: string | undefined;
    };
}>;
export declare const getSyncLogById: (userId: string, syncLogId: string) => Promise<{
    User: {
        Id: string;
        Name: string | null;
        Email: string;
    } | null;
    Company: {
        Id: string;
        Name: string;
    };
} & {
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    UserId: string | null;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    ApiEndpoint: string | null;
    Method: string | null;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Message: string | null;
    Duration: string | null;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    StartedAt: Date;
    CompletedAt: Date | null;
    CompanyId: string;
    RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
    ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
}>;
export declare const retrySyncOperation: (userId: string, syncLogId: string) => Promise<{
    success: boolean;
    message: string;
    data: {
        syncLogId: string;
        retryCount: number;
        maxRetries: number;
        status: import(".prisma/client").$Enums.SyncStatus;
    };
}>;
//# sourceMappingURL=syncLog.service.d.ts.map