export declare const SYNC_ENTITIES: readonly ["Accounts", "BankTransactions", "BankTransfers", "Budgets", "Contacts", "CreditNotes", "Currencies", "Employees", "ExpenseClaims", "Invoices", "Journals", "ManualJournals", "Items", "Payments", "PurchaseOrders", "TaxRates", "TrackingCategories", "Attachments", "Reports"];
export type SyncEntity = (typeof SYNC_ENTITIES)[number];
export interface EntitySyncStatus {
    entity: SyncEntity;
    lastSync: Date | null;
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'NEVER_SYNCED';
    recordCount?: number;
    errorMessage?: string;
}
export interface SyncTriggerRequest {
    entities: SyncEntity[];
    companyId: string;
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
    fullSync?: boolean;
}
export declare const getSyncStatus: (userId: string, companyId: string) => Promise<{
    companyId: string;
    companyName: string;
    connectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
    entities: EntitySyncStatus[];
    lastUpdated: Date;
}>;
export declare const triggerEntitySync: (userId: string, request: SyncTriggerRequest) => Promise<{
    success: boolean;
    message: string;
    data: {
        companyId: string;
        companyName: string;
        entities: ("Accounts" | "BankTransactions" | "BankTransfers" | "Budgets" | "Contacts" | "CreditNotes" | "Currencies" | "Employees" | "ExpenseClaims" | "Invoices" | "Journals" | "ManualJournals" | "Items" | "Payments" | "PurchaseOrders" | "TaxRates" | "TrackingCategories" | "Attachments" | "Reports")[];
        priority: "HIGH" | "NORMAL" | "LOW";
        fullSync: boolean;
        triggerIds: string[];
        estimatedDuration: string;
    };
}>;
export declare const triggerAllEntitiesSync: (userId: string, companyId: string, options?: {
    priority?: "HIGH" | "NORMAL" | "LOW";
    fullSync?: boolean;
}) => Promise<{
    success: boolean;
    message: string;
    data: {
        companyId: string;
        companyName: string;
        entities: ("Accounts" | "BankTransactions" | "BankTransfers" | "Budgets" | "Contacts" | "CreditNotes" | "Currencies" | "Employees" | "ExpenseClaims" | "Invoices" | "Journals" | "ManualJournals" | "Items" | "Payments" | "PurchaseOrders" | "TaxRates" | "TrackingCategories" | "Attachments" | "Reports")[];
        priority: "HIGH" | "NORMAL" | "LOW";
        fullSync: boolean;
        triggerIds: string[];
        estimatedDuration: string;
    };
}>;
export declare const getSyncHistory: (userId: string, companyId: string, options?: {
    limit?: number;
    offset?: number;
    entity?: SyncEntity;
    status?: string | undefined;
    dateFrom?: Date | undefined;
    dateTo?: Date | undefined;
}) => Promise<{
    companyId: string;
    companyName: string;
    history: {
        id: string;
        entity: string;
        status: import(".prisma/client").$Enums.SyncStatus;
        recordsProcessed: number;
        recordsSucceeded: number;
        recordsFailed: number;
        errorMessage: string | undefined;
        duration: string | undefined;
        createdAt: Date;
        completedAt: Date | undefined;
    }[];
    pagination: {
        totalCount: number;
        totalPages: number;
        currentPage: number;
        limit: number;
        offset: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
    appliedFilters: {
        entity: "Accounts" | "BankTransactions" | "BankTransfers" | "Budgets" | "Contacts" | "CreditNotes" | "Currencies" | "Employees" | "ExpenseClaims" | "Invoices" | "Journals" | "ManualJournals" | "Items" | "Payments" | "PurchaseOrders" | "TaxRates" | "TrackingCategories" | "Attachments" | "Reports" | undefined;
        status: string | undefined;
        dateFrom: Date | undefined;
        dateTo: Date | undefined;
    };
}>;
//# sourceMappingURL=sync.service.d.ts.map