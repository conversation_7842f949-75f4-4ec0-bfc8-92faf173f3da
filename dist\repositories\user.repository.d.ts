import { UserCreateData, UserUpdateData, UserEntity, SafeUserEntity } from '@models/dto/user.dto';
export declare const createUser: (data: UserCreateData) => Promise<UserEntity>;
export declare const findUserByEmail: (email: string) => Promise<UserEntity | null>;
export declare const logoutUser: (userId: string) => Promise<UserEntity>;
export declare const findUserById: (userId: string) => Promise<SafeUserEntity | null>;
export declare const updateUser: (userId: string, data: UserUpdateData) => Promise<UserEntity>;
//# sourceMappingURL=user.repository.d.ts.map