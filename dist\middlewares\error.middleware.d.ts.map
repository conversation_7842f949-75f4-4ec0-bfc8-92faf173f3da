{"version": 3, "file": "error.middleware.d.ts", "sourceRoot": "", "sources": ["../../src/middlewares/error.middleware.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAQ1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,GAAG,CAAC;CACf;AAKD,qBAAa,WAAY,SAAQ,KAAM,YAAW,QAAQ;IACxD,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,GAAG,CAAC;gBAGZ,OAAO,EAAE,MAAM,EACf,UAAU,GAAE,MAAY,EACxB,aAAa,GAAE,OAAc,EAC7B,IAAI,CAAC,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,GAAG;CAchB;AAKD,qBAAa,eAAgB,SAAQ,WAAW;gBAClC,OAAO,GAAE,MAAsB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG1E;AAED,qBAAa,iBAAkB,SAAQ,WAAW;gBACpC,OAAO,GAAE,MAAuB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3E;AAED,qBAAa,qBAAsB,SAAQ,WAAW;gBACxC,OAAO,GAAE,MAAuB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3E;AAED,qBAAa,cAAe,SAAQ,WAAW;gBACjC,OAAO,GAAE,MAAoB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAGxE;AAED,qBAAa,aAAc,SAAQ,WAAW;gBAChC,OAAO,GAAE,MAAoB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAGxE;AAED,qBAAa,aAAc,SAAQ,WAAW;gBAChC,OAAO,GAAE,MAAmB,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAGvE;AAED,qBAAa,eAAgB,SAAQ,WAAW;gBAClC,OAAO,GAAE,MAA2B,EAAE,OAAO,CAAC,EAAE,GAAG;CAGhE;AAED,qBAAa,mBAAoB,SAAQ,WAAW;gBACtC,OAAO,GAAE,MAAgC,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAGpF;AAMD,eAAO,MAAM,YAAY,GAAI,KAAK,QAAQ,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,OAAO,YAAY,SAqE3F,CAAC;AAMF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,SAM/E,CAAC;AAMF,eAAO,MAAM,iBAAiB,GAC5B,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,MAEnE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC"}