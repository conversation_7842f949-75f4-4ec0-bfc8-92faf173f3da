{"version": 3, "file": "sync.config.js", "sourceRoot": "", "sources": ["../../src/config/sync.config.ts"], "names": [], "mappings": ";;;AAKA,2CAAuC;AAE1B,QAAA,kBAAkB,GAA2B;IACtD,mBAAmB,EAAE,eAAM,CAAC,eAAe,CAAC,iCAAkC;IAC9E,gBAAgB,EAAE,eAAM,CAAC,eAAe,CAAC,8BAA+B;IACxE,cAAc,EAAE,eAAM,CAAC,eAAe,CAAC,4BAA6B;IACpE,WAAW,EAAE,eAAM,CAAC,eAAe,CAAC,yBAA0B;IAC9D,gBAAgB,EAAE,eAAM,CAAC,eAAe,CAAC,8BAA+B;IACxE,UAAU,EAAE,eAAM,CAAC,eAAe,CAAC,wBAAyB;IAC5D,UAAU,EAAE,eAAM,CAAC,eAAe,CAAC,wBAAyB;IAC5D,iBAAiB,EAAE,eAAM,CAAC,eAAe,CAAC,+BAAgC;IAC1E,UAAU,EAAE,eAAM,CAAC,eAAe,CAAC,wBAAyB;IAC5D,qBAAqB,EAAE,eAAM,CAAC,eAAe,CAAC,mCAAoC;IAClF,WAAW,EAAE,eAAM,CAAC,eAAe,CAAC,yBAA0B;IAC9D,aAAa,EAAE,eAAM,CAAC,eAAe,CAAC,2BAA4B;IAClE,YAAY,EAAE,eAAM,CAAC,eAAe,CAAC,2BAA4B;IAEjE,SAAS,EAAE,eAAM,CAAC,eAAe,CAAC,oBAAqB;IACvD,cAAc,EAAE,eAAM,CAAC,eAAe,CAAC,8BAA+B;IACtE,cAAc,EAAE,eAAM,CAAC,eAAe,CAAC,6BAA8B;CACxE,CAAC"}