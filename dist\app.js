"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const morgan_1 = __importDefault(require("morgan"));
const config_1 = require("./config/config");
const error_middleware_1 = require("./middlewares/error.middleware");
const requestLogger_middleware_1 = require("./middlewares/requestLogger.middleware");
const health_routes_1 = __importDefault(require("./routes/health.routes"));
const logger_1 = __importDefault(require("./utils/logger"));
const routes_1 = __importDefault(require("./routes"));
const createApp = () => {
    const app = (0, express_1.default)();
    app.set('trust proxy', 1);
    if (config_1.config.IS_DEVELOPMENT) {
        app.use((0, morgan_1.default)('dev'));
    }
    else {
        app.use((0, morgan_1.default)('combined', {
            stream: { write: (message) => logger_1.default.info(message.trim()) },
        }));
    }
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: config_1.config.IS_PRODUCTION ? true : false,
        crossOriginEmbedderPolicy: false,
    }));
    logger_1.default.info('CORS Configuration Applied', {
        origins: ['http://localhost:8080', 'http://localhost:3000'],
    });
    app.use((0, cors_1.default)({
        origin: ['http://localhost:8080', 'http://localhost:3000'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: [
            'Content-Type',
            'Authorization',
            'X-Correlation-ID',
            'X-Request-ID',
            'X-Client-Version',
            'X-Client-Platform',
            'X-Forwarded-For',
            'Accept',
            'Accept-Language',
            'Accept-Encoding',
            'Origin',
            'X-Requested-With',
            'Cache-Control',
            'Pragma',
            'Referer',
            'User-Agent',
            'Sec-CH-UA',
            'Sec-CH-UA-Mobile',
            'Sec-CH-UA-Platform',
        ],
        exposedHeaders: ['X-Correlation-ID'],
        optionsSuccessStatus: 200,
    }));
    app.use(express_1.default.json({
        limit: '10mb',
        verify: (req, _res, buf) => {
            req.rawBody = buf;
        },
    }));
    app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
    const limiter = (0, express_rate_limit_1.default)({
        windowMs: config_1.config.RATE_LIMIT.WINDOW_MS,
        max: config_1.config.RATE_LIMIT.MAX_REQUESTS,
        message: {
            success: false,
            error: 'Too many requests from this IP, please try again later.',
            data: {
                retryAfter: Math.ceil(config_1.config.RATE_LIMIT.WINDOW_MS / 1000),
            },
        },
        standardHeaders: true,
        legacyHeaders: false,
        skip: (req) => {
            return req.path.startsWith('/health');
        },
    });
    app.use(limiter);
    app.use(requestLogger_middleware_1.requestLogger);
    app.use('/health', health_routes_1.default);
    const apiPrefix = `${config_1.config.API.PREFIX}/${config_1.config.API.VERSION}`;
    app.use(`${apiPrefix}`, routes_1.default);
    app.use(error_middleware_1.notFoundHandler);
    app.use(error_middleware_1.errorHandler);
    return app;
};
const app = createApp();
exports.default = app;
//# sourceMappingURL=app.js.map