import { UserRegisterDTO, UserLoginDTO, UserEntity } from '@models/dto/user.dto';
export declare const registerUser: (input: UserRegisterDTO) => Promise<UserEntity>;
export declare const loginUser: (input: UserLoginDTO) => Promise<{
    user: UserEntity;
    token: string;
    tokenExpiry: Date;
}>;
export declare const logOutUser: (input: any) => Promise<{
    message: string;
}>;
export declare const getUserProfile: (userId: string) => Promise<import("@models/dto/user.dto").SafeUserEntity | null>;
//# sourceMappingURL=user.service.d.ts.map