import { z } from 'zod';
export declare const companyListQuerySchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    connectionStatus: z.ZodOptional<z.ZodEnum<["ACTIVE", "EXPIRED", "DISCONNECTED", "PENDING"]>>;
    hasXeroConnection: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean | undefined, string | undefined>;
    createdAfter: z.Zod<PERSON>ffects<z.ZodOptional<z.ZodString>, Date | undefined, string | undefined>;
    createdBefore: z.ZodEffects<z.ZodOptional<z.ZodString>, Date | undefined, string | undefined>;
    limit: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, number, string | undefined>, number, string | undefined>;
    offset: z.ZodEffects<z.ZodEffects<z.ZodOptional<z.ZodString>, number, string | undefined>, number, string | undefined>;
    sortBy: z.<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["name", "createdAt", "updatedAt"]>>>;
    sortOrder: z.ZodDefault<z.ZodOptional<z.ZodEnum<["asc", "desc"]>>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    offset: number;
    sortBy: "name" | "createdAt" | "updatedAt";
    sortOrder: "asc" | "desc";
    name?: string | undefined;
    connectionStatus?: "ACTIVE" | "EXPIRED" | "DISCONNECTED" | "PENDING" | undefined;
    hasXeroConnection?: boolean | undefined;
    createdAfter?: Date | undefined;
    createdBefore?: Date | undefined;
}, {
    name?: string | undefined;
    limit?: string | undefined;
    offset?: string | undefined;
    connectionStatus?: "ACTIVE" | "EXPIRED" | "DISCONNECTED" | "PENDING" | undefined;
    hasXeroConnection?: string | undefined;
    createdAfter?: string | undefined;
    createdBefore?: string | undefined;
    sortBy?: "name" | "createdAt" | "updatedAt" | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export type CompanyListQuery = z.infer<typeof companyListQuerySchema>;
export declare const createCompanySchema: z.ZodObject<{
    name: z.ZodString;
    financialYearEnd: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    financialYearEnd?: string | undefined;
}, {
    name: string;
    financialYearEnd?: string | undefined;
}>;
export declare const updateCompanySchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    financialYearEnd: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    financialYearEnd?: string | undefined;
}, {
    name?: string | undefined;
    financialYearEnd?: string | undefined;
}>;
export declare const companyIdSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
export type CreateCompanyData = z.infer<typeof createCompanySchema>;
export type UpdateCompanyData = z.infer<typeof updateCompanySchema>;
export type CompanyIdParams = z.infer<typeof companyIdSchema>;
//# sourceMappingURL=company.validator.d.ts.map