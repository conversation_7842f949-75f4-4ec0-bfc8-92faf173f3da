import { CreateSyncLogRequest } from '@services/syncLog.service';
export interface SyncContext {
    syncLogId: string;
    startTime: number;
    entity: string;
    integration: string;
    companyId: string;
    userId?: string;
}
export interface SyncResult {
    success: boolean;
    data?: any;
    error?: Error;
    warning?: string;
    recordsProcessed?: number;
}
export declare const startSyncOperation: (request: CreateSyncLogRequest) => Promise<SyncContext>;
export declare const completeSyncOperation: (context: SyncContext, result: SyncResult) => Promise<void>;
export declare const handleSyncFailure: (context: SyncContext, error: Error, shouldRetry?: boolean) => Promise<{
    retryScheduled: boolean;
}>;
export declare const withSyncLogging: <T>(request: CreateSyncLogRequest, operation: (context: SyncContext) => Promise<T>) => Promise<T>;
export declare const updateCompanySyncDates: (companyId: string, entity: string) => Promise<void>;
//# sourceMappingURL=syncLogger.d.ts.map