"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCompanySyncDates = exports.withSyncLogging = exports.handleSyncFailure = exports.completeSyncOperation = exports.startSyncOperation = void 0;
const client_1 = require("@prisma/client");
const syncLog_service_1 = require("@services/syncLog.service");
const logger_1 = __importDefault(require("@utils/logger"));
const startSyncOperation = async (request) => {
    try {
        const syncLog = await (0, syncLog_service_1.createSyncLog)(request);
        const context = {
            syncLogId: syncLog.Id,
            startTime: Date.now(),
            entity: request.entity,
            integration: request.integration,
            companyId: request.companyId,
            userId: request.userId,
        };
        await (0, syncLog_service_1.updateSyncLog)({
            id: syncLog.Id,
            status: client_1.SyncStatus.IN_PROGRESS,
            message: `${request.entity} sync started`,
        });
        logger_1.default.info('Sync operation started', {
            syncLogId: syncLog.Id,
            entity: request.entity,
            integration: request.integration,
            companyId: request.companyId,
        });
        return context;
    }
    catch (error) {
        logger_1.default.error('Failed to start sync operation', {
            request,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.startSyncOperation = startSyncOperation;
const completeSyncOperation = async (context, result) => {
    try {
        const duration = Date.now() - context.startTime;
        const durationStr = `${duration}ms`;
        let status;
        let message;
        if (result.success) {
            status = result.warning ? client_1.SyncStatus.WARNING : client_1.SyncStatus.SUCCESS;
            message = result.warning || `${context.entity} sync completed successfully`;
            if (result.recordsProcessed) {
                message += ` (${result.recordsProcessed} records processed)`;
            }
        }
        else {
            status = client_1.SyncStatus.ERROR;
            message = result.error?.message || `${context.entity} sync failed`;
        }
        await (0, syncLog_service_1.updateSyncLog)({
            id: context.syncLogId,
            status,
            message,
            duration: durationStr,
            responsePayload: result.data,
            errorDetails: result.error ? {
                name: result.error.name,
                message: result.error.message,
                stack: result.error.stack,
            } : undefined,
        });
        logger_1.default.info('Sync operation completed', {
            syncLogId: context.syncLogId,
            entity: context.entity,
            status,
            duration: durationStr,
            success: result.success,
        });
    }
    catch (error) {
        logger_1.default.error('Failed to complete sync operation', {
            context,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
    }
};
exports.completeSyncOperation = completeSyncOperation;
const handleSyncFailure = async (context, error, shouldRetry = true) => {
    try {
        const duration = Date.now() - context.startTime;
        const durationStr = `${duration}ms`;
        const errorDetails = {
            name: error.name,
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
        };
        if (shouldRetry) {
            await (0, syncLog_service_1.scheduleRetry)(context.syncLogId, errorDetails);
            logger_1.default.warn('Sync operation failed, retry scheduled', {
                syncLogId: context.syncLogId,
                entity: context.entity,
                error: error.message,
                duration: durationStr,
            });
            return { retryScheduled: true };
        }
        else {
            await (0, syncLog_service_1.updateSyncLog)({
                id: context.syncLogId,
                status: client_1.SyncStatus.ERROR,
                message: `${context.entity} sync failed: ${error.message}`,
                duration: durationStr,
                errorDetails,
            });
            logger_1.default.error('Sync operation failed, no retry scheduled', {
                syncLogId: context.syncLogId,
                entity: context.entity,
                error: error.message,
                duration: durationStr,
            });
            return { retryScheduled: false };
        }
    }
    catch (logError) {
        logger_1.default.error('Failed to handle sync failure', {
            context,
            originalError: error.message,
            logError: logError instanceof Error ? logError.message : 'Unknown error',
        });
        throw logError;
    }
};
exports.handleSyncFailure = handleSyncFailure;
const withSyncLogging = async (request, operation) => {
    let context = null;
    try {
        context = await (0, exports.startSyncOperation)(request);
        const result = await operation(context);
        await (0, exports.completeSyncOperation)(context, {
            success: true,
            data: result,
        });
        return result;
    }
    catch (error) {
        if (context) {
            await (0, exports.handleSyncFailure)(context, error, true);
        }
        throw error;
    }
};
exports.withSyncLogging = withSyncLogging;
const updateCompanySyncDates = async (companyId, entity) => {
    try {
        const { prisma } = await Promise.resolve().then(() => __importStar(require('@config/config')));
        const now = new Date();
        await prisma.company.update({
            where: { Id: companyId },
            data: {
                LastSyncDate: now,
                NextSyncDate: new Date(now.getTime() + 24 * 60 * 60 * 1000),
            },
        });
        logger_1.default.info('Company sync dates updated', {
            companyId,
            entity,
            lastSyncDate: now,
        });
    }
    catch (error) {
        logger_1.default.error('Failed to update company sync dates', {
            companyId,
            entity,
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.updateCompanySyncDates = updateCompanySyncDates;
//# sourceMappingURL=syncLogger.js.map