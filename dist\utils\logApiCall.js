"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logApiCall = void 0;
const config_1 = require("@config/config");
const logApiCall = async ({ companyId, userId, method, apiUrl, status, integrationName, apiName, requestPayload, responsePayload, duration, }) => {
    await config_1.prisma.apiLog.create({
        data: {
            CompanyId: companyId,
            UserId: userId ?? null,
            Method: method,
            ApiUrl: apiUrl,
            Status: status,
            IntegrationName: integrationName,
            ApiName: apiName,
            ApiRequest: requestPayload,
            ApiResponse: responsePayload,
            Duration: duration ?? null,
        },
    });
};
exports.logApiCall = logApiCall;
//# sourceMappingURL=logApiCall.js.map