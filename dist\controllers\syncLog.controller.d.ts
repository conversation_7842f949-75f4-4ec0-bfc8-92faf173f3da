import { Request, Response, NextFunction } from 'express';
export declare const getSyncLogsController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSyncLogDetailsController: (req: Request, res: Response, next: NextFunction) => void;
export declare const retrySyncController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSyncStatsController: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=syncLog.controller.d.ts.map