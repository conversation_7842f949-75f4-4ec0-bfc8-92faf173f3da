"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const axiosClient = axios_1.default.create({
    timeout: 15000,
    headers: {
        "Content-Type": "application/json",
    },
});
axiosClient.interceptors.request.use((config) => {
    return config;
});
axiosClient.interceptors.response.use((response) => response, (error) => {
    return Promise.reject(error);
});
exports.default = axiosClient;
//# sourceMappingURL=axiosClient.js.map