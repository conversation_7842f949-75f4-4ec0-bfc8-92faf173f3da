{"version": 3, "file": "security.middleware.js", "sourceRoot": "", "sources": ["../../src/middlewares/security.middleware.ts"], "names": [], "mappings": ";;;AACA,6CAA0C;AAC1C,yDAAqD;AAK9C,MAAM,kBAAkB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAEpF,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IAGjC,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACzC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;IAEpE,IAAI,eAAM,CAAC,aAAa,EAAE,CAAC;QACzB,GAAG,CAAC,SAAS,CAAC,2BAA2B,EAAE,qCAAqC,CAAC,CAAC;IACpF,CAAC;IAGD,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAClD,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5C,IACE,WAAW;YACX,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YACzC,CAAC,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAC5C,CAAC;YACD,OAAO,IAAI,CACT,IAAI,kCAAe,CACjB,wEAAwE,EACxE,sBAAsB,CACvB,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAGD,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;IACjE,IAAI,aAAa,GAAG,eAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAChD,OAAO,IAAI,CACT,IAAI,kCAAe,CACjB,sCAAsC,eAAM,CAAC,MAAM,CAAC,aAAa,QAAQ,EACzE,mBAAmB,CACpB,CACF,CAAC;IACJ,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA3CW,QAAA,kBAAkB,sBA2C7B"}