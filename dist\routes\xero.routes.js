"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const xero_controller_1 = require("@controllers/xero.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/connect', auth_middleware_1.authenticate, xero_controller_1.generateAuthUrl);
router.get('/callback', auth_middleware_1.authenticate, xero_controller_1.xeroCallback);
router.post('/disconnect', auth_middleware_1.authenticate, xero_controller_1.xeroDisconnect);
router.post('/refresh', auth_middleware_1.authenticate, xero_controller_1.refreshXeroToken);
exports.default = router;
//# sourceMappingURL=xero.routes.js.map