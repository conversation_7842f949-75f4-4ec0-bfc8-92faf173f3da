import { Request, Response, NextFunction } from 'express';
declare module 'express-serve-static-core' {
    interface Request {
        user?: any;
    }
}
export declare const getModuleSyncStatusController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSpecificModuleSyncStatusController: (req: Request, res: Response, next: NextFunction) => void;
export declare const updateModuleSyncTimeController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getAvailableModulesController: (req: Request, res: Response, next: NextFunction) => void;
export declare const getSyncSummaryController: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=xeroModuleSync.controller.d.ts.map